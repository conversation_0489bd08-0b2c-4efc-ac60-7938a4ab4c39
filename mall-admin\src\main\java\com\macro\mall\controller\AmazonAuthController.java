package com.macro.mall.controller;

import com.macro.mall.service.AmazonTokenServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/amazon")
public class AmazonAuthController {

    @Autowired
    private AmazonTokenServer amazonTokenServer;

    @GetMapping("/callback")
    public ResponseEntity<String> callback(
            @RequestParam("spapi_oauth_code") String code,
            @RequestParam("state") String memberIdStr) {

        Long memberId = Long.parseLong(memberIdStr);
        amazonTokenServer.handleAmazonCallback(code, memberId);
        return ResponseEntity.ok("授权成功，请返回平台继续操作");
    }
}
