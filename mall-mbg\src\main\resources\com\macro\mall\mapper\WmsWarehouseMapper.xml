<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.WmsWarehouseMapper">
    <resultMap id="WarehouseResultMap" type="com.macro.mall.model.WmsWarehouse">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="location" column="location"/>
        <result property="countryName" column="country_name"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <!-- 获取所有仓库的详细信息 -->
    <select id="getAllWarehouses" resultMap="WarehouseResultMap">
        SELECT id, name, location, create_time, status, country_name
        FROM wms_warehouse
    </select>

    <!-- 根据仓库 ID 获取指定仓库的详细信息 -->
    <select id="getWarehousesByIds" resultMap="WarehouseResultMap">
        SELECT id, name, location, create_time, status, country_name
        FROM wms_warehouse
        WHERE id IN
        <foreach item="item" index="index" collection="warehouseIds" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 插入仓库 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wms_warehouse (name, location, create_time, status)
        VALUES (#{name}, #{location}, #{createTime}, #{status})
    </insert>

    <!-- 更新仓库 -->
    <update id="update">
        UPDATE wms_warehouse
        SET name = #{name},
        location = #{location},
        create_time = #{createTime},
        status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 删除仓库 -->
    <delete id="delete">
        DELETE FROM wms_warehouse WHERE id = #{warehouseId}
    </delete>

</mapper>
