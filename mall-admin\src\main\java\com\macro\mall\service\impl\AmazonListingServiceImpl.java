package com.macro.mall.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.macro.mall.client.SPApiClient;
import com.macro.mall.dto.AmazonListingRequest;
import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.service.AmazonListingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

@Service
public class AmazonListingServiceImpl implements AmazonListingService {

    @Autowired
    private SPApiClient spApiClient;

    @Autowired
    private UmsMemberMapper umsMemberMapper;

    @Override
    public boolean uploadListing(AmazonListingRequest req) throws Exception {
        UmsMember member = umsMemberMapper.selectByPrimaryKey(req.getMemberId());

        // 构建请求路径
        String apiPath = "/listings/2021-08-01/items/" + member.getAmazonSellerId() + "/" + req.getSku();

        // 构建Amazon API请求体
        Map<String, Object> body = new HashMap<>();
        body.put("productType", "PRODUCT");

        // attributes 构建
        Map<String, Object> attributes = new HashMap<>();

        Map<String, Object> brand = new HashMap<>();
        brand.put("value", req.getBrand());
        attributes.put("brand", brand);

        Map<String, Object> itemName = new HashMap<>();
        itemName.put("value", req.getTitle());
        attributes.put("item_name", itemName);

        Map<String, Object> externalId = new HashMap<>();
        externalId.put("value", req.getGtin());
        externalId.put("type", "GTIN");
        attributes.put("external_product_id", externalId);

        Map<String, Object> description = new HashMap<>();
        description.put("value", req.getDescription());
        attributes.put("product_description", description);

        body.put("attributes", attributes);

        // listing 构建
        Map<String, Object> listing = new HashMap<>();
        listing.put("conditionType", req.getConditionType());
        listing.put("merchantShippingGroupName", "Default");
        listing.put("quantity", 100);

        Map<String, Object> price = new HashMap<>();
        price.put("currency", req.getCurrencyCode());
        price.put("amount", req.getPrice());
        listing.put("price", price);

        body.put("listing", listing);

        List<String> marketplaceIds = new ArrayList<>();
        marketplaceIds.add(req.getMarketplaceId());
        body.put("marketplaceIds", marketplaceIds);

        // JSON 转换
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);

        // 发送请求
        String response = spApiClient.doPut(req.getMemberId(), apiPath, jsonBody);

        return response != null && response.contains("status");
    }

}
