package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SHEIN添加自定义属性值请求DTO
 */
@Data
public class SheinAddCustomAttributeValueRequest {
    
    /**
     * 属性ID（必填）
     */
    @NotNull(message = "属性ID不能为空")
    @JsonProperty("attribute_id")
    private Long attributeId;
    
    /**
     * 默认语言属性值名称（必填）
     * 最多100个字符
     */
    @NotEmpty(message = "属性值不能为空")
    @JsonProperty("attribute_value")
    private String attributeValue;
    
    /**
     * 商品分类ID（必填）
     */
    @NotNull(message = "分类ID不能为空")
    @JsonProperty("category_id")
    private Long categoryId;
    
    /**
     * 属性值多语种名称（可选）
     */
    @JsonProperty("attribute_value_name_multis")
    private List<AttributeValueNameMulti> attributeValueNameMultis;
    
    @Data
    public static class AttributeValueNameMulti {
        /**
         * 语种
         */
        @JsonProperty("language")
        private String language;
        
        /**
         * 属性值多语种名称
         * 最多100个字符
         */
        @JsonProperty("attribute_value_name_multi")
        private String attributeValueNameMulti;
    }
}
