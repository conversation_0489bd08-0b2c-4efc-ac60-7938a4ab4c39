package com.macro.mall.controller;

import com.macro.mall.dto.AmazonListingRequest;
import com.macro.mall.service.AmazonListingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/amazon/listing")
public class AmazonListingController {

    @Autowired
    private AmazonListingService amazonListingService;

    @PostMapping("/upload")
    public ResponseEntity<?> uploadListing(@RequestBody AmazonListingRequest request) {
        try {
            boolean success = amazonListingService.uploadListing(request);
            return success
                    ? ResponseEntity.ok("上架成功")
                    : ResponseEntity.status(HttpStatus.BAD_REQUEST).body("上架失败");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("上架出错：" + e.getMessage());
        }
    }
}
