package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SHEIN查询店铺可售站点响应DTO
 */
@Data
public class SheinSiteList {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 站点数据列表
         */
        private List<SiteData> data;
        
        /**
         * 元数据信息
         */
        private Meta meta;
    }
    
    @Data
    public static class SiteData {
        /**
         * 主站点标识
         */
        @JsonProperty("main_site")
        private String mainSite;
        
        /**
         * 主站点名称
         */
        @JsonProperty("main_site_name")
        private String mainSiteName;
        
        /**
         * 子站点列表
         */
        @JsonProperty("sub_site_list")
        private List<SubSite> subSiteList;
    }
    
    @Data
    public static class SubSite {
        /**
         * 站点名称
         */
        @JsonProperty("site_name")
        private String siteName;
        
        /**
         * 站点缩写
         */
        @JsonProperty("site_abbr")
        private String siteAbbr;
        
        /**
         * 站点状态
         * 1: 启用, 0: 禁用
         */
        @JsonProperty("site_status")
        private Integer siteStatus;
        
        /**
         * 店铺类型
         */
        @JsonProperty("store_type")
        private Integer storeType;
        
        /**
         * 货币代码
         */
        @JsonProperty("currency")
        private String currency;
        
        /**
         * 货币符号（左侧）
         */
        @JsonProperty("symbol_left")
        private String symbolLeft;
        
        /**
         * 货币符号（右侧）
         */
        @JsonProperty("symbol_right")
        private String symbolRight;
    }
    
    @Data
    public static class Meta {
        /**
         * 数据总数
         */
        private Integer count;
        
        /**
         * 自定义对象
         */
        private Object customObj;
    }
}
