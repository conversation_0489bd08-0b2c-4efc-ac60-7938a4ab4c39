<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsCommentMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsComment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="member_nick_name" jdbcType="VARCHAR" property="memberNickName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="star" jdbcType="INTEGER" property="star" />
    <result column="member_ip" jdbcType="VARCHAR" property="memberIp" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="product_attribute" jdbcType="VARCHAR" property="productAttribute" />
    <result column="collect_couont" jdbcType="INTEGER" property="collectCouont" />
    <result column="read_count" jdbcType="INTEGER" property="readCount" />
    <result column="pics" jdbcType="VARCHAR" property="pics" />
    <result column="member_icon" jdbcType="VARCHAR" property="memberIcon" />
    <result column="replay_count" jdbcType="INTEGER" property="replayCount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.PmsComment">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_id, member_nick_name, product_name, star, member_ip, create_time, show_status, 
    product_attribute, collect_couont, read_count, pics, member_icon, replay_count
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.PmsCommentExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pms_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsCommentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pms_comment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_comment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsCommentExample">
    delete from pms_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsComment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_comment (product_id, member_nick_name, product_name, 
      star, member_ip, create_time, 
      show_status, product_attribute, collect_couont, 
      read_count, pics, member_icon, 
      replay_count, content)
    values (#{productId,jdbcType=BIGINT}, #{memberNickName,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{star,jdbcType=INTEGER}, #{memberIp,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{showStatus,jdbcType=INTEGER}, #{productAttribute,jdbcType=VARCHAR}, #{collectCouont,jdbcType=INTEGER}, 
      #{readCount,jdbcType=INTEGER}, #{pics,jdbcType=VARCHAR}, #{memberIcon,jdbcType=VARCHAR}, 
      #{replayCount,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsComment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        product_id,
      </if>
      <if test="memberNickName != null">
        member_nick_name,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="star != null">
        star,
      </if>
      <if test="memberIp != null">
        member_ip,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="productAttribute != null">
        product_attribute,
      </if>
      <if test="collectCouont != null">
        collect_couont,
      </if>
      <if test="readCount != null">
        read_count,
      </if>
      <if test="pics != null">
        pics,
      </if>
      <if test="memberIcon != null">
        member_icon,
      </if>
      <if test="replayCount != null">
        replay_count,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="memberNickName != null">
        #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        #{star,jdbcType=INTEGER},
      </if>
      <if test="memberIp != null">
        #{memberIp,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="productAttribute != null">
        #{productAttribute,jdbcType=VARCHAR},
      </if>
      <if test="collectCouont != null">
        #{collectCouont,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        #{readCount,jdbcType=INTEGER},
      </if>
      <if test="pics != null">
        #{pics,jdbcType=VARCHAR},
      </if>
      <if test="memberIcon != null">
        #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="replayCount != null">
        #{replayCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsCommentExample" resultType="java.lang.Long">
    select count(*) from pms_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_comment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.memberNickName != null">
        member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.star != null">
        star = #{record.star,jdbcType=INTEGER},
      </if>
      <if test="record.memberIp != null">
        member_ip = #{record.memberIp,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.productAttribute != null">
        product_attribute = #{record.productAttribute,jdbcType=VARCHAR},
      </if>
      <if test="record.collectCouont != null">
        collect_couont = #{record.collectCouont,jdbcType=INTEGER},
      </if>
      <if test="record.readCount != null">
        read_count = #{record.readCount,jdbcType=INTEGER},
      </if>
      <if test="record.pics != null">
        pics = #{record.pics,jdbcType=VARCHAR},
      </if>
      <if test="record.memberIcon != null">
        member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.replayCount != null">
        replay_count = #{record.replayCount,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update pms_comment
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      star = #{record.star,jdbcType=INTEGER},
      member_ip = #{record.memberIp,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      product_attribute = #{record.productAttribute,jdbcType=VARCHAR},
      collect_couont = #{record.collectCouont,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      pics = #{record.pics,jdbcType=VARCHAR},
      member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      replay_count = #{record.replayCount,jdbcType=INTEGER},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_comment
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      star = #{record.star,jdbcType=INTEGER},
      member_ip = #{record.memberIp,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      product_attribute = #{record.productAttribute,jdbcType=VARCHAR},
      collect_couont = #{record.collectCouont,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      pics = #{record.pics,jdbcType=VARCHAR},
      member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      replay_count = #{record.replayCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsComment">
    update pms_comment
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="memberNickName != null">
        member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        star = #{star,jdbcType=INTEGER},
      </if>
      <if test="memberIp != null">
        member_ip = #{memberIp,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="productAttribute != null">
        product_attribute = #{productAttribute,jdbcType=VARCHAR},
      </if>
      <if test="collectCouont != null">
        collect_couont = #{collectCouont,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        read_count = #{readCount,jdbcType=INTEGER},
      </if>
      <if test="pics != null">
        pics = #{pics,jdbcType=VARCHAR},
      </if>
      <if test="memberIcon != null">
        member_icon = #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="replayCount != null">
        replay_count = #{replayCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.PmsComment">
    update pms_comment
    set product_id = #{productId,jdbcType=BIGINT},
      member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      star = #{star,jdbcType=INTEGER},
      member_ip = #{memberIp,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      show_status = #{showStatus,jdbcType=INTEGER},
      product_attribute = #{productAttribute,jdbcType=VARCHAR},
      collect_couont = #{collectCouont,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      pics = #{pics,jdbcType=VARCHAR},
      member_icon = #{memberIcon,jdbcType=VARCHAR},
      replay_count = #{replayCount,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsComment">
    update pms_comment
    set product_id = #{productId,jdbcType=BIGINT},
      member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      star = #{star,jdbcType=INTEGER},
      member_ip = #{memberIp,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      show_status = #{showStatus,jdbcType=INTEGER},
      product_attribute = #{productAttribute,jdbcType=VARCHAR},
      collect_couont = #{collectCouont,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      pics = #{pics,jdbcType=VARCHAR},
      member_icon = #{memberIcon,jdbcType=VARCHAR},
      replay_count = #{replayCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>