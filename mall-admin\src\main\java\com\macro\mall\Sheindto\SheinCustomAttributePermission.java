package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SHEIN查询是否支持自定义属性值响应DTO
 */
@Data
public class SheinCustomAttributePermission {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 自定义属性权限数据列表
         */
        private List<CustomAttributePermissionData> data;
        
        /**
         * 元数据信息
         */
        private Meta meta;
    }
    
    @Data
    public static class CustomAttributePermissionData {
        /**
         * 是否有权限
         * 1-有权限，0-无权限
         */
        @JsonProperty("has_permission")
        private Integer hasPermission;
        
        /**
         * 最后一级类目ID
         */
        @JsonProperty("last_category_id")
        private Long lastCategoryId;
        
        /**
         * 属性ID
         */
        @JsonProperty("attribute_id")
        private Long attributeId;
    }
    
    @Data
    public static class Meta {
        /**
         * 总数量
         */
        private Integer count;
        
        /**
         * 自定义对象
         */
        private Object customObj;
    }
}
