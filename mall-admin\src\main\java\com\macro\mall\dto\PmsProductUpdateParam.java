package com.macro.mall.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PmsProductUpdateParam {
    @ApiModelProperty(value = "商品ID")
    private Long id;
    @ApiModelProperty(value = "货号")
    private String productSn;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品图片")
    private String pic;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "商品描述")
    private String description;
    @ApiModelProperty(value = "详情标题")
    private String detailTitle;
    @ApiModelProperty(value = "详细描述")
    private String detailDesc;
    @ApiModelProperty(value = "产品详情网页内容")
    private String detailHtml;
    @ApiModelProperty(value = "移动端网页详情")
    private String detailMobileHtml;
    @ApiModelProperty(value = "品牌ID")
    private Long brandId;
    @ApiModelProperty(value = "商品分类ID")
    private Long productCategoryId;
    @ApiModelProperty(value = "商品分类名称")
    private String productCategoryName;
    @ApiModelProperty(value = "商品父级分类ID")
    private Long productCateUplevelId;
    @ApiModelProperty(value = "商品父级分类名称")
    private String productCateUplevelName;
    @ApiModelProperty(value = "商品属性分类ID")
    private Long productAttributeCategoryId;
    @ApiModelProperty(value = "仓库ID")
    private Long warehouseId;
    @ApiModelProperty(value = "运费模板ID")
    private Long feightTemplateId;
    @ApiModelProperty(value = "国家/地区")
    private String location;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "销量")
    private Integer sale;
    @ApiModelProperty(value = "商品价格")
    private BigDecimal price;
    @ApiModelProperty(value = "市场价")
    private BigDecimal originalPrice;
    @ApiModelProperty(value = "促销价格")
    private BigDecimal promotionPrice;
    @ApiModelProperty(value = "库存")
    private Integer stock;
    @ApiModelProperty(value = "库存预警值")
    private Integer lowStock;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "高度-单位厘米")
    private BigDecimal height;
    @ApiModelProperty(value = "长度-单位厘米")
    private BigDecimal length;
    @ApiModelProperty(value = "宽度-单位厘米")
    private BigDecimal width;
    @ApiModelProperty(value = "体积-立方厘米")
    private BigDecimal size;
    @ApiModelProperty(value = "商品重量（克）")
    private BigDecimal weight;
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    @ApiModelProperty(value = "产品服务：1->无忧退货；2->快速退款；3->免费包邮")
    private String serviceIds;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "备注")
    private String note;
    @ApiModelProperty(value = "画册图片，最多5张，以逗号分割")
    private String albumPics;
    @ApiModelProperty(value = "上架状态：0->下架；1->上架")
    private Integer publishStatus;
    @ApiModelProperty(value = "审核状态：0->未审核；1->审核通过")
    private Integer verifyStatus;
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
    private Integer deleteStatus;
    @ApiModelProperty(value = "新品状态:0->不是新品；1->新品")
    private Integer newStatus;
    @ApiModelProperty(value = "推荐状态；0->不推荐；1->推荐")
    private Integer recommandStatus;
    @ApiModelProperty(value = "是否为预告商品：0->不是；1->是")
    private Integer previewStatus;
    @ApiModelProperty(value = "促销类型：0->无促销;1->促销价;2->会员价;3->阶梯价;4->满减价;5->限时购")
    private Integer promotionType;
    @ApiModelProperty(value = "促销开始时间")
    private Date promotionStartTime;
    @ApiModelProperty(value = "促销结束时间")
    private Date promotionEndTime;
    @ApiModelProperty(value = "活动限购数量")
    private Integer promotionPerLimit;
    @ApiModelProperty(value = "赠送的积分")
    private Integer giftPoint;
    @ApiModelProperty(value = "赠送的成长值")
    private Integer giftGrowth;
    @ApiModelProperty(value = "限制使用的积分数")
    private Integer usePointLimit;
    @ApiModelProperty(value = "上架时间")
    private Timestamp shelfTime;
    @ApiModelProperty(value = "品牌名称")
    private String brandName;
    @ApiModelProperty(value = "货币种类")
    private String currency;
}
