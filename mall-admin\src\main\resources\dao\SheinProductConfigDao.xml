<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.SheinProductConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.macro.mall.Sheindto.SheinProductConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="product_id" property="productId" jdbcType="BIGINT"/>
        <result column="shein_category_id" property="sheinCategoryId" jdbcType="BIGINT"/>
        <result column="shein_product_type_id" property="sheinProductTypeId" jdbcType="BIGINT"/>
        <result column="SPU_attribute_id" property="spuAttributeId" jdbcType="BIGINT"/>
        <result column="SKC_attribute_id" property="skcAttributeId" jdbcType="BIGINT"/>
        <result column="main_site" property="mainSite" jdbcType="VARCHAR"/>
        <result column="sub_site_list" property="subSiteList" jdbcType="VARCHAR"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="last_published_at" property="lastPublishedAt" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="VARCHAR"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, shein_category_id, shein_product_type_id, SPU_attribute_id, SKC_attribute_id, 
        main_site, sub_site_list, image_url, last_published_at, created_at, updated_at
    </sql>

    <!-- 插入商品配置 -->
    <insert id="insert" parameterType="com.macro.mall.Sheindto.SheinProductConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO shein_product_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="sheinCategoryId != null">shein_category_id,</if>
            <if test="sheinProductTypeId != null">shein_product_type_id,</if>
            <if test="spuAttributeId != null">SPU_attribute_id,</if>
            <if test="skcAttributeId != null">SKC_attribute_id,</if>
            <if test="mainSite != null and mainSite != ''">main_site,</if>
            <if test="subSiteList != null and subSiteList != ''">sub_site_list,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="lastPublishedAt != null and lastPublishedAt != ''">last_published_at,</if>
            <if test="createdAt != null and createdAt != ''">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId,jdbcType=BIGINT},</if>
            <if test="sheinCategoryId != null">#{sheinCategoryId,jdbcType=BIGINT},</if>
            <if test="sheinProductTypeId != null">#{sheinProductTypeId,jdbcType=BIGINT},</if>
            <if test="spuAttributeId != null">#{spuAttributeId,jdbcType=BIGINT},</if>
            <if test="skcAttributeId != null">#{skcAttributeId,jdbcType=BIGINT},</if>
            <if test="mainSite != null and mainSite != ''">#{mainSite,jdbcType=VARCHAR},</if>
            <if test="subSiteList != null and subSiteList != ''">#{subSiteList,jdbcType=VARCHAR},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl,jdbcType=VARCHAR},</if>
            <if test="lastPublishedAt != null and lastPublishedAt != ''">#{lastPublishedAt,jdbcType=VARCHAR},</if>
            <if test="createdAt != null and createdAt != ''">#{createdAt,jdbcType=VARCHAR},</if>
            <if test="updatedAt != null">#{updatedAt,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据商品ID查询配置 -->
    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shein_product_config
        WHERE product_id = #{productId}
    </select>

    <!-- 根据商品ID删除配置 -->
    <delete id="deleteByProductId">
        DELETE FROM shein_product_config WHERE product_id = #{productId}
    </delete>

    <!-- 根据商品ID更新配置 -->
    <update id="updateByProductId" parameterType="com.macro.mall.Sheindto.SheinProductConfig">
        UPDATE shein_product_config
        <set>
            <if test="sheinCategoryId != null">shein_category_id = #{sheinCategoryId,jdbcType=BIGINT},</if>
            <if test="sheinProductTypeId != null">shein_product_type_id = #{sheinProductTypeId,jdbcType=BIGINT},</if>
            <if test="spuAttributeId != null">SPU_attribute_id = #{spuAttributeId,jdbcType=BIGINT},</if>
            <if test="skcAttributeId != null">SKC_attribute_id = #{skcAttributeId,jdbcType=BIGINT},</if>
            <if test="mainSite != null and mainSite != ''">main_site = #{mainSite,jdbcType=VARCHAR},</if>
            <if test="subSiteList != null and subSiteList != ''">sub_site_list = #{subSiteList,jdbcType=VARCHAR},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl,jdbcType=VARCHAR},</if>
            <if test="lastPublishedAt != null and lastPublishedAt != ''">last_published_at = #{lastPublishedAt,jdbcType=VARCHAR},</if>
            <if test="createdAt != null and createdAt != ''">created_at = #{createdAt,jdbcType=VARCHAR},</if>
            updated_at = NOW()
        </set>
        WHERE product_id = #{productId}
    </update>

    <!-- 查询所有配置列表 -->
    <select id="selectAllConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shein_product_config
        ORDER BY updated_at DESC
    </select>

    <!-- 分页查询配置列表 -->
    <select id="selectConfigsWithPaging" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shein_product_config
        ORDER BY updated_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询配置总数 -->
    <select id="countConfigs" resultType="java.lang.Long">
        SELECT COUNT(*) FROM shein_product_config
    </select>

</mapper>
