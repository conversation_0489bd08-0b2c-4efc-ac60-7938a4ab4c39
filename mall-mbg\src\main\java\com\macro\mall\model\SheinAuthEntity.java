package com.macro.mall.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SheinAuthEntity {
    private Long id;                    // 主键
    private Long userId;               // 平台用户ID（归属）

    private Integer supplierId;         // SHEIN 店铺ID（关键业务主键）
    private String openKeyId;          // 授权返回：openKeyId（用于接口签名）
    private String secretKey;          // 授权返回：secretKey（加密存储）

    private String language;           // 默认语言（可选，用于接口请求）
    private Integer supplierSource;     // 供应商来源

    private Boolean valid;             // 是否仍有效（可用于禁用或失效标记 ）
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
}
