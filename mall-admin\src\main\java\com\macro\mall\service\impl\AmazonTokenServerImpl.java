package com.macro.mall.service.impl;

import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.service.AmazonTokenServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AmazonTokenServerImpl implements AmazonTokenServer {

    @Value("${amazon.client_id}")
    private String clientId;

    @Value("${amazon.client_secret}")
    private String clientSecret;

    @Value("${amazon.redirect_uri}")
    private String redirectUri;

    @Autowired
    private UmsMemberMapper umsMemberMapper;

    @Override
    public void handleAmazonCallback(String code, Long memberId) {
        RestTemplate restTemplate = new RestTemplate();

        // 1. 用 authorization_code 换 access_token 和 refresh_token
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "authorization_code");
        params.add("code", code);
        params.add("client_id", clientId);
        params.add("client_secret", clientSecret);
        params.add("redirect_uri", redirectUri);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        ResponseEntity<Map> tokenResponse = restTemplate.postForEntity(
                "https://api.amazon.com/auth/o2/token", request, Map.class);

        Map<String, Object> tokenBody = tokenResponse.getBody();
        String accessToken = (String) tokenBody.get("access_token");
        String refreshToken = (String) tokenBody.get("refresh_token");
        Integer expiresIn = (Integer) tokenBody.get("expires_in");

        Date tokenExpireTime = Date.from(Instant.now().plusSeconds(expiresIn));

        // 2. 用 access_token 获取卖家 sellerId
        HttpHeaders authHeaders = new HttpHeaders();
        authHeaders.setBearerAuth(accessToken);
        HttpEntity<Void> authRequest = new HttpEntity<>(authHeaders);

        ResponseEntity<Map> sellerResponse = restTemplate.exchange(
                "https://sellingpartnerapi-na.amazon.com/sellers/v1/marketplaceParticipations",
                HttpMethod.GET, authRequest, Map.class);

        List<Map<String, Object>> payload = (List<Map<String, Object>>) sellerResponse.getBody().get("payload");
        String sellerId = (String) payload.get(0).get("sellerId");

        // 3. 更新数据库用户表
        UmsMember member = umsMemberMapper.selectByPrimaryKey(memberId);
        member.setAmazonAccessToken(accessToken);
        member.setAmazonRefreshToken(refreshToken);
        member.setAmazonTokenExpireTime(tokenExpireTime);
        member.setAmazonSellerId(sellerId);
        umsMemberMapper.updateByPrimaryKey(member);
    }
}
