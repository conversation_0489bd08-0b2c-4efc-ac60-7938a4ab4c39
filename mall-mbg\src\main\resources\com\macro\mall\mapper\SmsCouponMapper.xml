<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SmsCouponMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.SmsCoupon">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="per_limit" jdbcType="INTEGER" property="perLimit" />
    <result column="min_point" jdbcType="DECIMAL" property="minPoint" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="use_type" jdbcType="INTEGER" property="useType" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="publish_count" jdbcType="INTEGER" property="publishCount" />
    <result column="use_count" jdbcType="INTEGER" property="useCount" />
    <result column="receive_count" jdbcType="INTEGER" property="receiveCount" />
    <result column="enable_time" jdbcType="TIMESTAMP" property="enableTime" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="member_level" jdbcType="INTEGER" property="memberLevel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, name, platform, count, amount, per_limit, min_point, start_time, end_time, 
    use_type, note, publish_count, use_count, receive_count, enable_time, code, member_level
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.SmsCouponExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sms_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sms_coupon
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sms_coupon
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.SmsCouponExample">
    delete from sms_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.SmsCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon (type, name, platform, 
      count, amount, per_limit, 
      min_point, start_time, end_time, 
      use_type, note, publish_count, 
      use_count, receive_count, enable_time, 
      code, member_level)
    values (#{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, 
      #{count,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{perLimit,jdbcType=INTEGER}, 
      #{minPoint,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{useType,jdbcType=INTEGER}, #{note,jdbcType=VARCHAR}, #{publishCount,jdbcType=INTEGER}, 
      #{useCount,jdbcType=INTEGER}, #{receiveCount,jdbcType=INTEGER}, #{enableTime,jdbcType=TIMESTAMP}, 
      #{code,jdbcType=VARCHAR}, #{memberLevel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.SmsCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="perLimit != null">
        per_limit,
      </if>
      <if test="minPoint != null">
        min_point,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="useType != null">
        use_type,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="publishCount != null">
        publish_count,
      </if>
      <if test="useCount != null">
        use_count,
      </if>
      <if test="receiveCount != null">
        receive_count,
      </if>
      <if test="enableTime != null">
        enable_time,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="memberLevel != null">
        member_level,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="perLimit != null">
        #{perLimit,jdbcType=INTEGER},
      </if>
      <if test="minPoint != null">
        #{minPoint,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useType != null">
        #{useType,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="publishCount != null">
        #{publishCount,jdbcType=INTEGER},
      </if>
      <if test="useCount != null">
        #{useCount,jdbcType=INTEGER},
      </if>
      <if test="receiveCount != null">
        #{receiveCount,jdbcType=INTEGER},
      </if>
      <if test="enableTime != null">
        #{enableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="memberLevel != null">
        #{memberLevel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.SmsCouponExample" resultType="java.lang.Long">
    select count(*) from sms_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sms_coupon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.count != null">
        count = #{record.count,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.perLimit != null">
        per_limit = #{record.perLimit,jdbcType=INTEGER},
      </if>
      <if test="record.minPoint != null">
        min_point = #{record.minPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.useType != null">
        use_type = #{record.useType,jdbcType=INTEGER},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.publishCount != null">
        publish_count = #{record.publishCount,jdbcType=INTEGER},
      </if>
      <if test="record.useCount != null">
        use_count = #{record.useCount,jdbcType=INTEGER},
      </if>
      <if test="record.receiveCount != null">
        receive_count = #{record.receiveCount,jdbcType=INTEGER},
      </if>
      <if test="record.enableTime != null">
        enable_time = #{record.enableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.memberLevel != null">
        member_level = #{record.memberLevel,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sms_coupon
    set id = #{record.id,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      count = #{record.count,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=DECIMAL},
      per_limit = #{record.perLimit,jdbcType=INTEGER},
      min_point = #{record.minPoint,jdbcType=DECIMAL},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      use_type = #{record.useType,jdbcType=INTEGER},
      note = #{record.note,jdbcType=VARCHAR},
      publish_count = #{record.publishCount,jdbcType=INTEGER},
      use_count = #{record.useCount,jdbcType=INTEGER},
      receive_count = #{record.receiveCount,jdbcType=INTEGER},
      enable_time = #{record.enableTime,jdbcType=TIMESTAMP},
      code = #{record.code,jdbcType=VARCHAR},
      member_level = #{record.memberLevel,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.SmsCoupon">
    update sms_coupon
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="perLimit != null">
        per_limit = #{perLimit,jdbcType=INTEGER},
      </if>
      <if test="minPoint != null">
        min_point = #{minPoint,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useType != null">
        use_type = #{useType,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="publishCount != null">
        publish_count = #{publishCount,jdbcType=INTEGER},
      </if>
      <if test="useCount != null">
        use_count = #{useCount,jdbcType=INTEGER},
      </if>
      <if test="receiveCount != null">
        receive_count = #{receiveCount,jdbcType=INTEGER},
      </if>
      <if test="enableTime != null">
        enable_time = #{enableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="memberLevel != null">
        member_level = #{memberLevel,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.SmsCoupon">
    update sms_coupon
    set type = #{type,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      count = #{count,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      per_limit = #{perLimit,jdbcType=INTEGER},
      min_point = #{minPoint,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      use_type = #{useType,jdbcType=INTEGER},
      note = #{note,jdbcType=VARCHAR},
      publish_count = #{publishCount,jdbcType=INTEGER},
      use_count = #{useCount,jdbcType=INTEGER},
      receive_count = #{receiveCount,jdbcType=INTEGER},
      enable_time = #{enableTime,jdbcType=TIMESTAMP},
      code = #{code,jdbcType=VARCHAR},
      member_level = #{memberLevel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>