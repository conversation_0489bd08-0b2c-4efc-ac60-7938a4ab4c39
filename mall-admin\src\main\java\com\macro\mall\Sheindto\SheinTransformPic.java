package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * SHEIN图片链接转换响应DTO
 */
@Data
public class SheinTransformPic {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 原始图片链接
         */
        @JsonProperty("original")
        private String original;
        
        /**
         * 转换后的图片链接
         */
        @JsonProperty("transformed")
        private String transformed;
        
        /**
         * 失败原因（转换失败时提供）
         */
        @JsonProperty("failure_reason")
        private String failureReason;
    }
}
