package com.macro.mall.service;

import com.macro.mall.dto.PendingPublishDTO;
import com.macro.mall.model.PendingPublishEntity;
import com.macro.mall.model.PlatformEnum;

import java.util.List;
import java.util.Map;

public interface PendingPublishService {
    List<Map<Long, String>> getPlatformList();

    List<Integer> getShopsByPlatform(Long platformId);

    void addPendingPublish(PendingPublishDTO pendingPublishDTO);

    void cleanPendingPublish();

    List<PendingPublishEntity> showPendingPublishList();

    boolean cleanPendingPublishByProductId(Long productId);
}
