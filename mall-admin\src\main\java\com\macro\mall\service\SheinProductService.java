package com.macro.mall.service;

import com.macro.mall.Sheindto.*;
import com.macro.mall.dto.SheinProductCreateDTO;
import com.macro.mall.dto.SheinProductResultDTO;

public interface SheinProductService {
    SheinProductResultDTO createProduct(SheinProductCreateDTO productDTO);

    /**
     * 获取商品发布规范
     * @param request 请求参数
     * @return 发布规范信息
     */
    SheinPublishSpec getPublishSpec(SheinPublishSpecRequest request);

    /**
     * 获取可用品牌列表
     * @return 品牌列表信息
     */
    SheinBrandList getBrands();

    /**
     * 获取类目树
     * @return 类目树信息
     */
    SheinCategoryTree getCategoryTree();

    /**
     * 查询店铺可选属性
     * @param request 请求参数，包含产品类型ID列表
     * @return 属性模板信息
     */
    SheinAttributeTemplate getAttributeTemplate(SheinAttributeTemplateRequest request);

    /**
     * 查询是否支持自定义属性值
     * @param request 请求参数，包含类目ID列表
     * @return 自定义属性权限配置信息
     */
    SheinCustomAttributePermission getCustomAttributePermission(SheinCustomAttributePermissionRequest request);

    /**
     * 添加自定义属性值
     * @param request 请求参数，包含属性ID、属性值、分类ID等
     * @return 添加结果信息
     */
    SheinAddCustomAttributeValue addCustomAttributeValue(SheinAddCustomAttributeValueRequest request);

    /**
     * 本地图片上传
     * @param request 请求参数，包含图片类型和图片文件
     * @return 上传结果信息
     */
    SheinUploadPic uploadPic(SheinUploadPicRequest request);

    /**
     * 图片链接转换
     * @param request 请求参数，包含图片类型和原始图片链接
     * @return 转换结果信息
     */
    SheinTransformPic transformPic(SheinTransformPicRequest request);

    /**
     * 查询店铺可售站点
     * @param request 请求参数（空对象）
     * @return 站点列表信息
     */
    SheinSiteList getSiteList(SheinSiteListRequest request);

    /**
     * 查询商家仓库列表(自主运营和半托管模式)
     * @param request 请求参数（空对象）
     * @return 仓库列表信息
     */
    SheinWarehouseList getWarehouseList(SheinWarehouseListRequest request);

    /**
     * 商品发布或编辑
     * @param request 商品发布请求参数
     * @return 发布结果信息
     */
    SheinPublishOrEditResponse publishOrEdit(SheinPublishOrEditRequest request);
}
