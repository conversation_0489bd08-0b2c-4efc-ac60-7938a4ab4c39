<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SmsCouponHistoryMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.SmsCouponHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="member_nickname" jdbcType="VARCHAR" property="memberNickname" />
    <result column="get_type" jdbcType="INTEGER" property="getType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="use_status" jdbcType="INTEGER" property="useStatus" />
    <result column="use_time" jdbcType="TIMESTAMP" property="useTime" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, coupon_id, member_id, coupon_code, member_nickname, get_type, create_time, use_status, 
    use_time, order_id, order_sn
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.SmsCouponHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sms_coupon_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sms_coupon_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sms_coupon_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.SmsCouponHistoryExample">
    delete from sms_coupon_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.SmsCouponHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon_history (coupon_id, member_id, coupon_code, 
      member_nickname, get_type, create_time, 
      use_status, use_time, order_id, 
      order_sn)
    values (#{couponId,jdbcType=BIGINT}, #{memberId,jdbcType=BIGINT}, #{couponCode,jdbcType=VARCHAR}, 
      #{memberNickname,jdbcType=VARCHAR}, #{getType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{useStatus,jdbcType=INTEGER}, #{useTime,jdbcType=TIMESTAMP}, #{orderId,jdbcType=BIGINT}, 
      #{orderSn,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.SmsCouponHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_coupon_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="memberId != null">
        member_id,
      </if>
      <if test="couponCode != null">
        coupon_code,
      </if>
      <if test="memberNickname != null">
        member_nickname,
      </if>
      <if test="getType != null">
        get_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="useStatus != null">
        use_status,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="memberNickname != null">
        #{memberNickname,jdbcType=VARCHAR},
      </if>
      <if test="getType != null">
        #{getType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useStatus != null">
        #{useStatus,jdbcType=INTEGER},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.SmsCouponHistoryExample" resultType="java.lang.Long">
    select count(*) from sms_coupon_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sms_coupon_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=BIGINT},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.couponCode != null">
        coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.memberNickname != null">
        member_nickname = #{record.memberNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.getType != null">
        get_type = #{record.getType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.useStatus != null">
        use_status = #{record.useStatus,jdbcType=INTEGER},
      </if>
      <if test="record.useTime != null">
        use_time = #{record.useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSn != null">
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sms_coupon_history
    set id = #{record.id,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=BIGINT},
      member_id = #{record.memberId,jdbcType=BIGINT},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      member_nickname = #{record.memberNickname,jdbcType=VARCHAR},
      get_type = #{record.getType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      use_status = #{record.useStatus,jdbcType=INTEGER},
      use_time = #{record.useTime,jdbcType=TIMESTAMP},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_sn = #{record.orderSn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.SmsCouponHistory">
    update sms_coupon_history
    <set>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="memberNickname != null">
        member_nickname = #{memberNickname,jdbcType=VARCHAR},
      </if>
      <if test="getType != null">
        get_type = #{getType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useStatus != null">
        use_status = #{useStatus,jdbcType=INTEGER},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.SmsCouponHistory">
    update sms_coupon_history
    set coupon_id = #{couponId,jdbcType=BIGINT},
      member_id = #{memberId,jdbcType=BIGINT},
      coupon_code = #{couponCode,jdbcType=VARCHAR},
      member_nickname = #{memberNickname,jdbcType=VARCHAR},
      get_type = #{getType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      use_status = #{useStatus,jdbcType=INTEGER},
      use_time = #{useTime,jdbcType=TIMESTAMP},
      order_id = #{orderId,jdbcType=BIGINT},
      order_sn = #{orderSn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>