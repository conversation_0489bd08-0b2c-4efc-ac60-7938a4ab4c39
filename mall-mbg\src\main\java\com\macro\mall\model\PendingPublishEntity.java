package com.macro.mall.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class PendingPublishEntity {

    @ApiModelProperty(value ="主键ID")
    private Long id; //
    @ApiModelProperty(value ="用户ID")
    private Long userId; //
    @ApiModelProperty(value ="商品ID")
    private Long productId; //
    @ApiModelProperty(value ="店铺ID")
    private Long supplierId; //
    @ApiModelProperty(value ="平台ID")
    private Long platformId; //
    @ApiModelProperty(value ="名称")
    private String name; //
    @ApiModelProperty(value ="状态(0:待更新,1:已更新,2:待审核,3:失败)")
    private Integer status; //
    @ApiModelProperty(value ="上架商品数量")
    private Integer number; //
    @ApiModelProperty(value ="创建时间")
    private LocalDateTime createdAt; //
    @ApiModelProperty(value ="更新时间")
    private LocalDateTime updatedAt; //
}