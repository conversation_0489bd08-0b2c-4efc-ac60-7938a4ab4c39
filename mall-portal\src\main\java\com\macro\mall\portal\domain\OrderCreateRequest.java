package com.macro.mall.portal.domain;

import com.macro.mall.model.OmsOrderCreateParam;

public class OrderCreateRequest {
    private String username;
    private String password;
    private OmsOrderCreateParam creatorder;

    // Getters and Setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public OmsOrderCreateParam getCreatorder() { return creatorder; }
    public void setCreatorder(OmsOrderCreateParam creatorder) { this.creatorder = creatorder; }
}
