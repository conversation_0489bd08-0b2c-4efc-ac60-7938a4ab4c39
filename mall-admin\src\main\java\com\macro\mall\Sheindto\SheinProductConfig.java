package com.macro.mall.Sheindto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SHEIN商品配置实体类
 */
@Data
@ApiModel(value = "SheinProductConfig对象", description = "SHEIN商品配置")
public class SheinProductConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "SHEIN类目ID")
    private Long sheinCategoryId;

    @ApiModelProperty(value = "SHEIN商品类型ID")
    private Long sheinProductTypeId;

    @ApiModelProperty(value = "SPU属性ID")
    private Long spuAttributeId;

    @ApiModelProperty(value = "SKC属性ID")
    private Long skcAttributeId;

    @ApiModelProperty(value = "主站点")
    private String mainSite;

    @ApiModelProperty(value = "子站点列表")
    private String subSiteList;

    @ApiModelProperty(value = "图片URL")
    private String imageUrl;

    @ApiModelProperty(value = "最后发布时间")
    private String lastPublishedAt;

    @ApiModelProperty(value = "创建时间")
    private String createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
}
