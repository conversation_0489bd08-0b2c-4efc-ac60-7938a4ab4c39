package com.macro.mall.portal.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.model.OmsCartItem;
import com.macro.mall.portal.domain.CartProduct;
import com.macro.mall.portal.domain.CartPromotionItem;
import com.macro.mall.portal.service.OmsCartItemService;
import com.macro.mall.portal.service.UmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车管理Controller
 * Created by macro on 2018/8/2.
 */
@Controller
@Api(tags = "OmsCartItemController")
@Tag(name = "OmsCartItemController", description = "购物车管理")
@RequestMapping("/cart")
public class OmsCartItemController {
//    @Autowired
//    private OmsCartItemService cartItemService;
//    @Autowired
//    private UmsMemberService memberService;
//
//    @ApiOperation("添加商品到购物车")
//    @RequestMapping(value = "/add", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult add(@RequestBody OmsCartItem cartItem) {
//        int count = cartItemService.add(cartItem);
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.failed();
//    }
//
//    @ApiOperation("获取当前会员的购物车列表")
//    @RequestMapping(value = "/list", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult<List<OmsCartItem>> list() {
//        List<OmsCartItem> cartItemList = cartItemService.list(memberService.getCurrentMember().getId());
//        return CommonResult.success(cartItemList);
//    }
//
//    @ApiOperation("获取当前会员的购物车列表,包括促销信息")
//    @RequestMapping(value = "/list/promotion", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult<List<CartPromotionItem>> listPromotion(@RequestParam(required = false) List<Long> cartIds) {
//        List<CartPromotionItem> cartPromotionItemList = cartItemService.listPromotion(memberService.getCurrentMember().getId(), cartIds);
//        return CommonResult.success(cartPromotionItemList);
//    }
//
//    @ApiOperation("修改购物车中指定商品的数量")
//    @RequestMapping(value = "/update/quantity", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult updateQuantity(@RequestParam Long id,
//                                       @RequestParam Integer quantity) {
//        int count = cartItemService.updateQuantity(id, memberService.getCurrentMember().getId(), quantity);
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.failed();
//    }
//
//    @ApiOperation("获取购物车中指定商品的规格,用于重选规格")
//    @RequestMapping(value = "/getProduct/{productId}", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult<CartProduct> getCartProduct(@PathVariable Long productId) {
//        CartProduct cartProduct = cartItemService.getCartProduct(productId);
//        return CommonResult.success(cartProduct);
//    }
//
//    @ApiOperation("修改购物车中商品的规格")
//    @RequestMapping(value = "/update/attr", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult updateAttr(@RequestBody OmsCartItem cartItem) {
//        int count = cartItemService.updateAttr(cartItem);
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.failed();
//    }
//
//    @ApiOperation("删除购物车中的指定商品")
//    @RequestMapping(value = "/delete", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult delete(@RequestParam("ids") List<Long> ids) {
//        int count = cartItemService.delete(memberService.getCurrentMember().getId(), ids);
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.failed();
//    }
//
//    @ApiOperation("清空当前会员的购物车")
//    @RequestMapping(value = "/clear", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult clear() {
//        int count = cartItemService.clear(memberService.getCurrentMember().getId());
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.failed();
//    }
}
