package com.macro.mall.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class ApiLogLimiter {
    private final Map<String, AtomicInteger> logCountMap = new ConcurrentHashMap<>();
    private final Map<String, Long> timestampMap = new ConcurrentHashMap<>();

    private static final int MAX_LOGS_PER_MINUTE = 300;

    public boolean allow(String key) {
        long currentMinute = System.currentTimeMillis() / 60000;

        timestampMap.putIfAbsent(key, currentMinute);
        logCountMap.putIfAbsent(key, new AtomicInteger(0));

        if (timestampMap.get(key) != currentMinute) {
            // 时间进入下一分钟，重置计数器
            timestampMap.put(key, currentMinute);
            logCountMap.get(key).set(0);
        }

        return logCountMap.get(key).incrementAndGet() <= MAX_LOGS_PER_MINUTE;
    }
}
