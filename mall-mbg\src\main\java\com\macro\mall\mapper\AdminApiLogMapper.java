package com.macro.mall.mapper;

import com.macro.mall.model.AdminApiLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AdminApiLogMapper {
    @Insert("INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) " +
            "VALUES(#{username}, #{apiPath}, #{httpMethod}, #{operation}, #{requestParam}, #{ipAddress}, NOW())")
    void insert(AdminApiLog log);


}
