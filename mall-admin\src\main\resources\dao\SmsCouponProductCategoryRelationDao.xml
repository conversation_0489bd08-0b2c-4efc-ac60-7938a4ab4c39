<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.SmsCouponProductCategoryRelationDao">
    <insert id="insertList">
        INSERT INTO sms_coupon_product_category_relation (product_category_id,product_category_name,parent_category_name,coupon_id) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productCategoryId,jdbcType=BIGINT},
            #{item.productCategoryName,jdbcType=VARCHAR},
            #{item.parentCategoryName,jdbcType=VARCHAR},
            #{item.couponId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>