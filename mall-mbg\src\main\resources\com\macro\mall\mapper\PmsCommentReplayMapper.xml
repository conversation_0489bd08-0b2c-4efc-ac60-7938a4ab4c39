<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsCommentReplayMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsCommentReplay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="comment_id" jdbcType="BIGINT" property="commentId" />
    <result column="member_nick_name" jdbcType="VARCHAR" property="memberNickName" />
    <result column="member_icon" jdbcType="VARCHAR" property="memberIcon" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, comment_id, member_nick_name, member_icon, content, create_time, type
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsCommentReplayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_comment_replay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pms_comment_replay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_comment_replay
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsCommentReplayExample">
    delete from pms_comment_replay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsCommentReplay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_comment_replay (comment_id, member_nick_name, member_icon, 
      content, create_time, type
      )
    values (#{commentId,jdbcType=BIGINT}, #{memberNickName,jdbcType=VARCHAR}, #{memberIcon,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsCommentReplay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_comment_replay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        comment_id,
      </if>
      <if test="memberNickName != null">
        member_nick_name,
      </if>
      <if test="memberIcon != null">
        member_icon,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        #{commentId,jdbcType=BIGINT},
      </if>
      <if test="memberNickName != null">
        #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="memberIcon != null">
        #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsCommentReplayExample" resultType="java.lang.Long">
    select count(*) from pms_comment_replay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_comment_replay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.commentId != null">
        comment_id = #{record.commentId,jdbcType=BIGINT},
      </if>
      <if test="record.memberNickName != null">
        member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.memberIcon != null">
        member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_comment_replay
    set id = #{record.id,jdbcType=BIGINT},
      comment_id = #{record.commentId,jdbcType=BIGINT},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsCommentReplay">
    update pms_comment_replay
    <set>
      <if test="commentId != null">
        comment_id = #{commentId,jdbcType=BIGINT},
      </if>
      <if test="memberNickName != null">
        member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="memberIcon != null">
        member_icon = #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsCommentReplay">
    update pms_comment_replay
    set comment_id = #{commentId,jdbcType=BIGINT},
      member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      member_icon = #{memberIcon,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>