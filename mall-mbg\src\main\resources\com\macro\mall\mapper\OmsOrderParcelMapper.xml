<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.OmsOrderParcelMapper">
    <resultMap id="BaseResultMap" type="com.macro.mall.model.OmsOrderParcel">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="order_id" jdbcType="BIGINT" property="orderId" />
        <result column="warehouse_id" jdbcType="VARCHAR" property="warehouseId" />
        <result column="location" jdbcType="VARCHAR" property="location" />
        <result column="parcel_sn" jdbcType="VARCHAR" property="parcelSn" />
        <result column="parcel_status" jdbcType="BIGINT" property="parcelStatus" />
        <result column="shipping_time" jdbcType="TIMESTAMP" property="shippingTime" />
        <result column="received_time" jdbcType="TIMESTAMP" property="receivedTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="delete_status" jdbcType="BIGINT" property="deleteStatus" />
    </resultMap>
</mapper>