<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PendingPublishMapper">
    <delete id="deleteByUserIdAndProductId">
        DELETE FROM pms_pending_publish
        WHERE user_id = #{userId}
          AND product_id = #{productId}
    </delete>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO pms_pending_publish
        (user_id, product_id, supplier_id, platform_id, name, status, number, created_at, updated_at)
        VALUES (#{userId}, #{productId}, #{supplierId}, #{platformId}, #{name}, #{status}, #{number}, #{createdAt},
                #{updatedAt})
    </insert>
    <update id="updateNumberById">
        UPDATE pms_pending_publish
        SET number     = #{number},
            updated_at = NOW()
        WHERE id = #{id}
    </update>
    <select id="selectByUserIdAndProductId" resultType="com.macro.mall.model.PendingPublishEntity">
        SELECT * FROM pms_pending_publish
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="productId != null">
                AND product_id = #{productId}
            </if>
            <if test="supplierId != null">
                AND supplier_id = #{supplierId}
            </if>
            <if test="platformId != null">
                AND platform_id = #{platformId}
            </if>
            <if test="number != null">
                AND number = #{number}
            </if>
        </where>
    </select>


</mapper>