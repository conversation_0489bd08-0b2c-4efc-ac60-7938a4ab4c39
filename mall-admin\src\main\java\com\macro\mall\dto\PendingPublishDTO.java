package com.macro.mall.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PendingPublishDTO {
    @ApiModelProperty(value ="商品ID")
    private Long productId; //
    @ApiModelProperty(value ="店铺ID")
    private Long supplierId; //
    @ApiModelProperty(value ="平台ID")
    private Long platformId; //
    @ApiModelProperty(value ="上架商品数量")
    private Integer number; //
}
