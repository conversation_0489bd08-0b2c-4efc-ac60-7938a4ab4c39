<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsMemberLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsMemberLoginLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="login_type" jdbcType="INTEGER" property="loginType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_id, create_time, ip, city, login_type, province
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsMemberLoginLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_member_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_member_login_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_member_login_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsMemberLoginLogExample">
    delete from ums_member_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsMemberLoginLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_login_log (member_id, create_time, ip, 
      city, login_type, province
      )
    values (#{memberId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{ip,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{loginType,jdbcType=INTEGER}, #{province,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsMemberLoginLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_login_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        member_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="loginType != null">
        login_type,
      </if>
      <if test="province != null">
        province,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="loginType != null">
        #{loginType,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsMemberLoginLogExample" resultType="java.lang.Long">
    select count(*) from ums_member_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_member_login_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.loginType != null">
        login_type = #{record.loginType,jdbcType=INTEGER},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_member_login_log
    set id = #{record.id,jdbcType=BIGINT},
      member_id = #{record.memberId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      ip = #{record.ip,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      login_type = #{record.loginType,jdbcType=INTEGER},
      province = #{record.province,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsMemberLoginLog">
    update ums_member_login_log
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="loginType != null">
        login_type = #{loginType,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsMemberLoginLog">
    update ums_member_login_log
    set member_id = #{memberId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      ip = #{ip,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      login_type = #{loginType,jdbcType=INTEGER},
      province = #{province,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>