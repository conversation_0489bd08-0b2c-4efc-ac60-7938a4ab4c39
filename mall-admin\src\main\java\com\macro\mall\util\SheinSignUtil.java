package com.macro.mall.util;

import org.springframework.util.DigestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class SheinSignUtil {

    public static String generateTokenExchangeSignature(String appId, String appSecret, String timestamp) {
        String raw = appId + timestamp + appSecret;
        return DigestUtils.md5DigestAsHex(raw.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }
    public static String generateSheinSignature(String openKeyId, String secretKey, String path,
                                                String timestamp, String randomKey) throws Exception {

        // 步骤一：组装签名数据VALUE
        String value = openKeyId + "&" + timestamp + "&" + path;
        System.out.println("步骤一 - 签名数据VALUE: " + value);

        // 步骤二：组装签名密钥KEY
        String key = secretKey + randomKey;
        System.out.println("步骤二 - 签名密钥KEY: " + key);

        // 步骤三：HMAC-SHA256计算并转换为十六进制
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hmacResult = mac.doFinal(value.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexStringBuilder = new StringBuilder();
        for (byte b : hmacResult) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexStringBuilder.append('0');
            }
            hexStringBuilder.append(hex);
        }
        String hexSignature = hexStringBuilder.toString();
        System.out.println("步骤三 - HMAC-SHA256结果(HEX): " + hexSignature);

        // 步骤四：Base64编码
        String base64Signature = Base64.getEncoder().encodeToString(
                hexSignature.getBytes(StandardCharsets.UTF_8));
        System.out.println("步骤四 - Base64编码结果: " + base64Signature);

        // 步骤五：拼接RandomKey
        String finalSignature = randomKey + base64Signature;
        System.out.println("步骤五 - 最终签名: " + finalSignature);

        return finalSignature;
    }
}
