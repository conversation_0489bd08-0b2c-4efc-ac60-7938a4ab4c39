package com.macro.mall.Sheindto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * SHEIN本地图片上传请求DTO
 */
@Data
public class SheinUploadPicRequest {
    
    /**
     * 图片类型（必填）
     * 1:主图; 2:细节图; 5:方块图; 6:色块图; 7:详情图
     */
    @NotNull(message = "图片类型不能为空")
    private Integer imageType;
    
    /**
     * 图片文件（必填）
     */
    @NotNull(message = "图片文件不能为空")
    private MultipartFile file;
}
