package com.macro.mall.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.macro.mall.Sheindto.*;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.config.SheinClientConfig;
import com.macro.mall.config.SheinConfig;
import com.macro.mall.constants.SheinApiPath;
import com.macro.mall.dto.SheinProductCreateDTO;
import com.macro.mall.dto.SheinProductResultDTO;
import com.macro.mall.util.AESUtil;
import com.macro.mall.util.SheinSignUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SheinApiClient {
    @Resource
    private SheinConfig sheinConfig;

    private final OkHttpClient httpClient = new OkHttpClient();

    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String ENCODING = "UTF-8";
    //由于调用getbytoken接口时，开发者还没有openkeyId和secretKey，请使用appId和appSecretKey生成签名
    public CommonResult getByToken(String tempToken, String appId, String timestamp, String sign) throws IOException {
        //String url = "https://openapi-sem.sheincorp.com/open-api/auth/get-by-token";
        //TODO url为测试url
        String url = SheinApiPath.TEST_SERVER+SheinApiPath.GET_BY_TOKEN_API_PATH;

        Map<String, String> headers = new HashMap<>();

        // 封装请求头
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("x-lt-appid", appId);
        headers.put("x-lt-timestamp", timestamp);
        headers.put("x-lt-signature", sign);

        String responseStr = "";
        try {
            responseStr = doPost(url, headers, "{\"tempToken\":\"" + tempToken + "\"}");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        CommonResult response = new com.fasterxml.jackson.databind.ObjectMapper().readValue(responseStr, CommonResult.class);

        return response;


    }

    public SheinProductResultDTO createProduct(String openKeyId, String secretKey, SheinProductCreateDTO productDTO) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/product/create"; // 示例，需确认实际接口地址

        String jsonBody = objectMapper.writeValueAsString(productDTO);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN商品创建接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            // TODO: 解析响应，根据SHEIN返回结构组装返回DTO
            // 这里简单模拟：
            SheinProductResultDTO result = new SheinProductResultDTO();
            result.setSuccess(true);
            result.setMessage("上架成功");
            result.setSheinProductId("shein123456");
            return result;
        }
    }
    //封装发送请求
    public JsonNode sendRequest(SheinClientConfig sheinConfig, String path, String method, Object requestBodyObject) throws IOException {
        String url = sheinConfig.getBaseUrl() + path;
        String requestBodyJson = objectMapper.writeValueAsString(requestBodyObject);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = null;
        try {
            signature = SheinSignUtil.generateSheinSignature(
                    method.toUpperCase(), path, timestamp, requestBodyJson, sheinConfig.getSecretKey());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
        }

        RequestBody body = requestBodyJson != null && !"GET".equalsIgnoreCase(method)
                ? RequestBody.create(requestBodyJson, MediaType.parse("application/json"))
                : null;

        Map<String, String> headers = buildSheinHeaders(sheinConfig, timestamp, signature);

        Request.Builder requestBuilder = new Request.Builder().url(url);
        headers.forEach(requestBuilder::addHeader);

        if ("POST".equalsIgnoreCase(method)) {
            requestBuilder.post(body);
        } else if ("GET".equalsIgnoreCase(method)) {
            requestBuilder.get();
        } else {
            throw new UnsupportedOperationException("不支持的方法: " + method);
        }

        OkHttpClient clientWithTimeout = httpClient.newBuilder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .build();

        try (Response response = clientWithTimeout.newCall(requestBuilder.build()).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN请求失败，状态码：{}", response.code());
                throw new IOException("SHEIN请求失败，状态码：" + response.code());
            }
            return objectMapper.readTree(response.body().string());
        }
    }

    private Map<String, String> buildSheinHeaders(SheinClientConfig config, String timestamp, String signature) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("language", config.getDefaultLanguage());
        headers.put("x-lt-openKeyId", config.getOpenKeyId());
        headers.put("x-lt-timestamp", timestamp);
        headers.put("x-lt-signature", signature);
        return headers;
    }



    public String doPost(String url, Map<String, String> headers, String json) throws Exception {
        // 创建httpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建http对象
        HttpPost httpPost = new HttpPost(url);
        /**
         * setConnectTimeout：设置连接超时时间，单位毫秒。
         * setConnectionRequestTimeout：设置从connect Manager(连接池)获取Connection
         * 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
         * setSocketTimeout：请求获取数据的超时时间(即响应时间)，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
         */
        RequestConfig requestConfig = RequestConfig.custom().build();
        httpPost.setConfig(requestConfig);
        // 封装请求头
        if (headers != null) {
            Set<Map.Entry<String, String>> entrySet = headers.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                // 设置到请求头到HttpRequestBase对象中
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }

        // 封装请求参数
        if (json != null) {
            // 设置到请求的http对象中
            httpPost.setEntity(new StringEntity(json, ENCODING));
        }

        try (CloseableHttpResponse httpResponse = httpClient.execute(httpPost);) {
            // 执行请求并获得响应结果
            return EntityUtils.toString(httpResponse.getEntity(), ENCODING);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取商品发布规范
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数
     * @return 发布规范信息
     * @throws IOException IO异常
     */
    public SheinPublishSpec getPublishSpec(String openKeyId, String secretKey, SheinPublishSpecRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/query-publish-fill-in-standard";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN获取发布规范接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN获取发布规范响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinPublishSpec.class);
        }
    }

    /**
     * 获取可用品牌列表
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @return 品牌列表信息
     * @throws IOException IO异常
     */
    public SheinBrandList getBrands(String openKeyId, String secretKey) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/query-brand-list";

        // 空请求体
        String jsonBody = "{}";

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN获取品牌列表接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN获取品牌列表响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinBrandList.class);
        }
    }

    /**
     * 获取类目树
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @return 类目树信息
     * @throws IOException IO异常
     */
    public SheinCategoryTree getCategoryTree(String openKeyId, String secretKey) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/query-category-tree";

        // 空请求体
        String jsonBody = "{}";

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN获取类目树接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN获取类目树响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinCategoryTree.class);
        }
    }

    /**
     * 查询店铺可选属性
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数，包含产品类型ID列表
     * @return 属性模板信息
     * @throws IOException IO异常
     */
    public SheinAttributeTemplate getAttributeTemplate(String openKeyId, String secretKey, SheinAttributeTemplateRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/query-attribute-template";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN查询店铺可选属性接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN查询店铺可选属性响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinAttributeTemplate.class);
        }
    }

    /**
     * 查询是否支持自定义属性值
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数，包含类目ID列表
     * @return 自定义属性权限配置信息
     * @throws IOException IO异常
     */
    public SheinCustomAttributePermission getCustomAttributePermission(String openKeyId, String secretKey, SheinCustomAttributePermissionRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/get-custom-attribute-permission-config";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN查询是否支持自定义属性值接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN查询是否支持自定义属性值响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinCustomAttributePermission.class);
        }
    }

    /**
     * 添加自定义属性值
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数，包含属性ID、属性值、分类ID等
     * @return 添加结果信息
     * @throws IOException IO异常
     */
    public SheinAddCustomAttributeValue addCustomAttributeValue(String openKeyId, String secretKey, SheinAddCustomAttributeValueRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/add-custom-attribute-value";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN添加自定义属性值接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN添加自定义属性值响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinAddCustomAttributeValue.class);
        }
    }

    /**
     * 本地图片上传
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数，包含图片类型和图片文件
     * @return 上传结果信息
     * @throws IOException IO异常
     */
    public SheinUploadPic uploadPic(String openKeyId, String secretKey, SheinUploadPicRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/upload-pic";

        // 构建multipart/form-data请求体
        MultipartBody.Builder bodyBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image_type", request.getImageType().toString());

        // 添加文件部分
        if (request.getFile() != null && !request.getFile().isEmpty()) {
            String fileName = request.getFile().getOriginalFilename();
            if (fileName == null || fileName.isEmpty()) {
                fileName = "upload.jpg";
            }

            RequestBody fileBody = RequestBody.create(
                request.getFile().getBytes(),
                MediaType.parse(request.getFile().getContentType())
            );
            bodyBuilder.addFormDataPart("file", fileName, fileBody);
        } else {
            throw new IOException("图片文件不能为空");
        }

        RequestBody requestBody = bodyBuilder.build();

        // 对于文件上传，需要特殊处理签名
        // 这里使用空字符串作为body进行签名计算，因为multipart数据难以序列化
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, "");

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN本地图片上传接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN本地图片上传响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinUploadPic.class);
        }
    }

    /**
     * 图片链接转换
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数，包含图片类型和原始图片链接
     * @return 转换结果信息
     * @throws IOException IO异常
     */
    public SheinTransformPic transformPic(String openKeyId, String secretKey, SheinTransformPicRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/transform-pic";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN图片链接转换接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN图片链接转换响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinTransformPic.class);
        }
    }

    /**
     * 查询店铺可售站点
     * @param openKeyId 开放平台密钥ID
     * @param secretKey 密钥
     * @param request 请求参数（空对象）
     * @return 站点列表信息
     * @throws IOException IO异常
     */
    public SheinSiteList getSiteList(String openKeyId, String secretKey, SheinSiteListRequest request) throws IOException {
        String url = "https://openapi-sem.sheincorp.com/open-api/goods/query-site-list";

        String jsonBody = objectMapper.writeValueAsString(request);

        // 计算签名
        String signature = SheinSignUtil.generateTokenExchangeSignature(openKeyId, secretKey, jsonBody);

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .addHeader("x-lt-signature", signature)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("SHEIN查询店铺可售站点接口失败，响应码: {}", response.code());
                throw new IOException("接口调用失败，响应码：" + response.code());
            }
            String responseBody = response.body().string();
            log.info("SHEIN查询店铺可售站点响应: {}", responseBody);

            // 解析响应并返回
            return objectMapper.readValue(responseBody, SheinSiteList.class);
        }
    }
}
