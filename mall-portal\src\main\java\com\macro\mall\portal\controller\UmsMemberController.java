package com.macro.mall.portal.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.model.UmsMember;
import com.macro.mall.portal.domain.LoginParam;
import com.macro.mall.portal.service.UmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * 会员管理Controller
 * Created by macro on 2018/8/3.
 */
@Controller
@Api(tags = "UmsMemberController")
@Tag(name = "UmsMemberController", description = "会员登录注册管理")
@RequestMapping("/sso")
public class UmsMemberController {
    @Value("${jwt.tokenHeader}")
    private String tokenHeader;
    @Value("${jwt.tokenHead}")
    private String tokenHead;
    @Autowired
    private UmsMemberService memberService;

//    @ApiOperation("会员注册")
//    @RequestMapping(value = "/register", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult register(@RequestParam String username,
//                                 @RequestParam String password,
//                                 @RequestParam String telephone,
//                                 @RequestParam String authCode) {
//        memberService.register(username, password, telephone, authCode);
//        return CommonResult.success(null,"注册成功");
//    }

//    @ApiOperation("会员登录")
//    @PostMapping("/login")
//    @ResponseBody
//    public CommonResult login(@RequestBody LoginParam loginParam) {
//        String token = memberService.login(loginParam.getUsername(), loginParam.getPassword());
//        if (token == null) {
//            return CommonResult.validateFailed("用户名或密码错误");
//        }
//        Map<String, String> tokenMap = new HashMap<>();
//        tokenMap.put("token", token);
//        tokenMap.put("tokenHead", tokenHead);
//        return CommonResult.success(tokenMap);
//    }

//    @ApiOperation("获取会员信息")
//    @RequestMapping(value = "/info", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult info(Principal principal) {
//        if(principal==null){
//            return CommonResult.unauthorized(null);
//        }
//        UmsMember member = memberService.getCurrentMember();
//        return CommonResult.success(member);
//    }

//    @ApiOperation("获取验证码")
//    @RequestMapping(value = "/getAuthCode", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult getAuthCode(@RequestParam String telephone) {
//        String authCode = memberService.generateAuthCode(telephone);
//        return CommonResult.success(authCode,"获取验证码成功");
//    }
//
//    @ApiOperation("会员修改密码")
//    @RequestMapping(value = "/updatePassword", method = RequestMethod.POST)
//    @ResponseBody
//    public CommonResult updatePassword(@RequestParam String telephone,
//                                 @RequestParam String password,
//                                 @RequestParam String authCode) {
//        memberService.updatePassword(telephone,password,authCode);
//        return CommonResult.success(null,"密码修改成功");
//    }
//
//
//    @ApiOperation(value = "刷新token")
//    @RequestMapping(value = "/refreshToken", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult refreshToken(HttpServletRequest request) {
//        String token = request.getHeader(tokenHeader);
//        String refreshToken = memberService.refreshToken(token);
//        if (refreshToken == null) {
//            return CommonResult.failed("token已经过期！");
//        }
//        Map<String, String> tokenMap = new HashMap<>();
//        tokenMap.put("token", refreshToken);
//        tokenMap.put("tokenHead", tokenHead);
//        return CommonResult.success(tokenMap);
//    }
}
