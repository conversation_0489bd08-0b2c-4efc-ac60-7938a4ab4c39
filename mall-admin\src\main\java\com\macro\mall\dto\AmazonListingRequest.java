package com.macro.mall.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AmazonListingRequest {
    private Long memberId;        // 零售商用户ID
    private String sku;           // 商品SKU
    private String title;         // 标题
    private String description;   // 描述
    private BigDecimal price;     // 价格
    private String currencyCode;  // 货币（USD）
    private String brand;
    private String gtin;          // 商品条码
    private String conditionType; // 商品状态，例如 "new_new"
    private String marketplaceId; // 市场ID，比如 "ATVPDKIKX0DER" (美国)
}
