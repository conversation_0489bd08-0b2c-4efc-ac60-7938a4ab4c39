package com.macro.mall.service;

import com.macro.mall.mapper.AdminApiLogMapper;
import com.macro.mall.model.AdminApiLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AdminLogService {

    @Autowired
    private AdminApiLogMapper adminApiLogMapper;

    @Async
    public void saveApiLog(String username, String apiPath, String method, String operation,
                           String requestParam, String ip, Date time) {
        AdminApiLog log = new AdminApiLog();
        log.setUsername(username);
        log.setApiPath(apiPath);
        log.setHttpMethod(method);
        log.setOperation(operation);
        log.setRequestParam(requestParam);
        log.setIpAddress(ip);
        log.setCreateTime(time);
        adminApiLogMapper.insert(log);
    }
}
