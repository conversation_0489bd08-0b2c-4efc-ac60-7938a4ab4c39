package com.macro.mall.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SHEIN商品配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("shein_product_config")
@ApiModel(value = "SheinProductConfig对象", description = "SHEIN商品配置")
public class SheinProductConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "商品ID")
    @TableField("product_id")
    private Long productId;

    @ApiModelProperty(value = "SHEIN类目ID")
    @TableField("shein_category_id")
    private Long sheinCategoryId;

    @ApiModelProperty(value = "SHEIN商品类型ID")
    @TableField("shein_product_type_id")
    private Long sheinProductTypeId;

    @ApiModelProperty(value = "SPU属性ID")
    @TableField("SPU_attribute_id")
    private Long spuAttributeId;

    @ApiModelProperty(value = "SKC属性ID")
    @TableField("SKC_attribute_id")
    private Long skcAttributeId;

    @ApiModelProperty(value = "主站点")
    @TableField("main_site")
    private String mainSite;

    @ApiModelProperty(value = "子站点列表")
    @TableField("sub_site_list")
    private String subSiteList;

    @ApiModelProperty(value = "图片URL")
    @TableField("image_url")
    private String imageUrl;

    @ApiModelProperty(value = "最后发布时间")
    @TableField("last_published_at")
    private String lastPublishedAt;

    @ApiModelProperty(value = "创建时间")
    @TableField("created_at")
    private String createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
