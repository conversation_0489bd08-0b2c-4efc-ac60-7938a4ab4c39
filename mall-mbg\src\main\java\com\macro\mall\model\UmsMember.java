package com.macro.mall.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class UmsMember implements Serializable {
    private Long id;
    private Long memberLevelId;
    private String username;
    private String password;
    private String amazonSellerId;
    private String amazonRefreshToken;
    private String amazonAccessToken;
    private Date amazonTokenExpireTime;
    private String nickname;
    private String phone;
    private Integer status;
    private Date createTime;
    private String icon;
    private Integer gender;
    private Integer sourceType;
    private Integer integration;
    private Integer growth;
    private Integer luckeyCount;
    private Integer historyIntegration;

    // ---------- Getter & Setter ----------

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAmazonSellerId() {
        return amazonSellerId;
    }

    public void setAmazonSellerId(String amazonSellerId) {
        this.amazonSellerId = amazonSellerId;
    }

    public String getAmazonRefreshToken() {
        return amazonRefreshToken;
    }

    public void setAmazonRefreshToken(String amazonRefreshToken) {
        this.amazonRefreshToken = amazonRefreshToken;
    }

    public String getAmazonAccessToken() {
        return amazonAccessToken;
    }

    public void setAmazonAccessToken(String amazonAccessToken) {
        this.amazonAccessToken = amazonAccessToken;
    }

    public Date getAmazonTokenExpireTime() {
        return amazonTokenExpireTime;
    }

    public void setAmazonTokenExpireTime(Date amazonTokenExpireTime) {
        this.amazonTokenExpireTime = amazonTokenExpireTime;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getIntegration() {
        return integration;
    }

    public void setIntegration(Integer integration) {
        this.integration = integration;
    }

    public Integer getGrowth() {
        return growth;
    }

    public void setGrowth(Integer growth) {
        this.growth = growth;
    }

    public Integer getLuckeyCount() {
        return luckeyCount;
    }

    public void setLuckeyCount(Integer luckeyCount) {
        this.luckeyCount = luckeyCount;
    }

    public Integer getHistoryIntegration() {
        return historyIntegration;
    }

    public void setHistoryIntegration(Integer historyIntegration) {
        this.historyIntegration = historyIntegration;
    }

    // ---------- toString ----------

    @Override
    public String toString() {
        return "UmsMember{" +
                "id=" + id +
                ", memberLevelId=" + memberLevelId +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", amazonSellerId='" + amazonSellerId + '\'' +
                ", amazonRefreshToken='" + amazonRefreshToken + '\'' +
                ", amazonAccessToken='" + amazonAccessToken + '\'' +
                ", amazonTokenExpireTime=" + amazonTokenExpireTime +
                ", nickname='" + nickname + '\'' +
                ", phone='" + phone + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", icon='" + icon + '\'' +
                ", gender=" + gender +
                ", sourceType=" + sourceType +
                ", integration=" + integration +
                ", growth=" + growth +
                ", luckeyCount=" + luckeyCount +
                ", historyIntegration=" + historyIntegration +
                '}';
    }
}
