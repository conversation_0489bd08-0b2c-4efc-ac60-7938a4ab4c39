<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.portal.dao.PortalOrderDao">
    <resultMap id="orderDetailMap" type="com.macro.mall.portal.domain.OmsOrderDetail"
               extends="com.macro.mall.mapper.OmsOrderMapper.BaseResultMap">
        <collection property="orderItemList" columnPrefix="ot_"
                    resultMap="com.macro.mall.mapper.OmsOrderItemMapper.BaseResultMap">
        </collection>
    </resultMap>
    <select id="getDetail" resultMap="orderDetailMap">
        SELECT
            o.id,
            o.order_sn,
            o.coupon_id,
            o.integration,
            o.member_id,
            ot.id ot_id,
            ot.product_name ot_product_name,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.product_quantity ot_product_quantity
        FROM
            oms_order o
            LEFT JOIN oms_order_item ot ON o.id = ot.order_id
        WHERE
            o.id = #{orderId}
    </select>

    <select id="getTimeOutOrders" resultMap="orderDetailMap">
        SELECT
            o.id,
            o.order_sn,
            o.coupon_id,
            o.integration,
            o.member_id,
            o.use_integration,
            ot.id               ot_id,
            ot.product_name     ot_product_name,
            ot.product_sku_id   ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.product_quantity ot_product_quantity
        FROM
            oms_order o
            LEFT JOIN oms_order_item ot ON o.id = ot.order_id
        WHERE
            o.status = 0
            AND o.create_time &lt; date_add(NOW(), INTERVAL -#{minute} MINUTE);
    </select>

    <update id="updateSkuStock">
        UPDATE pms_sku_stock
        SET
            stock = CASE id
            <foreach collection="itemList" item="item">
              WHEN #{item.productSkuId} THEN stock - #{item.productQuantity}
            </foreach>
            END,
            lock_stock = CASE id
            <foreach collection="itemList" item="item">
              WHEN #{item.productSkuId} THEN lock_stock - #{item.productQuantity}
            </foreach>
            END
        WHERE
            id IN
        <foreach collection="itemList" item="item" separator="," open="(" close=")">
            #{item.productSkuId}
        </foreach>
    </update>
    <update id="updateOrderStatus">
        update oms_order
        set status=#{status}
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
    <update id="releaseSkuStockLock">
        UPDATE pms_sku_stock
        SET
        lock_stock = CASE id
        <foreach collection="itemList" item="item">
            WHEN #{item.productSkuId} THEN lock_stock - #{item.productQuantity}
        </foreach>
        END
        WHERE
        id IN
        <foreach collection="itemList" item="item" separator="," open="(" close=")">
            #{item.productSkuId}
        </foreach>
    </update>
    <update id="lockStockBySkuId">
        UPDATE pms_sku_stock
        SET lock_stock = lock_stock + #{quantity}
        WHERE
        id = #{productSkuId}
        AND lock_stock + #{quantity} &lt;= stock
    </update>
    <update id="reduceSkuStock">
        UPDATE pms_sku_stock
        SET lock_stock = lock_stock - #{quantity},
            stock = stock - #{quantity}
        WHERE
            id = #{productSkuId}
          AND stock - #{quantity} &gt;= 0
          AND lock_stock - #{quantity} &gt;= 0
    </update>
    <update id="releaseStockBySkuId">
        UPDATE pms_sku_stock
        SET lock_stock = lock_stock - #{quantity}
        WHERE
            id = #{productSkuId}
          AND lock_stock - #{quantity} &gt;= 0
    </update>
</mapper>