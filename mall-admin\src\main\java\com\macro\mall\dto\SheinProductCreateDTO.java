package com.macro.mall.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SheinProductCreateDTO {

    @NotNull
    private Long categoryId;

    @NotBlank
    private String supplierCode;

    @NotNull
    private List<MultiLanguageName> multiLanguageNameList;

    @NotNull
    private List<MultiLanguageDesc> multiLanguageDescList;

    @NotNull
    private List<ProductAttribute> productAttributeList;

    @NotNull
    private List<Skc> skcList;

    @NotNull
    private List<Site> siteList;

    // 可选：source_system，默认值
    private String sourceSystem = "OpenAPI";

    @Data
    public static class MultiLanguageName {
        @NotBlank
        private String language;
        @NotBlank
        private String name;
    }

    @Data
    public static class MultiLanguageDesc {
        @NotBlank
        private String language;
        @NotBlank
        private String name;
    }

    @Data
    public static class ProductAttribute {
        @NotNull
        private Long attributeId;
        @NotNull
        private Long attributeValueId;
        private String attributeExtraValue;
    }

    @Data
    public static class Site {
        private String mainSite;
        private List<String> subSiteList;
    }

    @Data
    public static class Skc {
        @NotBlank
        private String supplierCode;
        @NotNull
        private SaleAttribute saleAttribute;
        @NotNull
        private List<Sku> skuList;

        @Data
        public static class SaleAttribute {
            @NotNull
            private Long attributeId;
            @NotNull
            private Long attributeValueId;
        }

        @Data
        public static class Sku {
            @NotBlank
            private String supplierSku;
            @NotNull
            private Double basePrice;
            @NotBlank
            private String currency;
            @NotNull
            private Integer inventoryNum;
            private String supplierWarehouseId;

            private Double specialPrice;
            private String mallState = "1"; // 默认在售
            private String length;
            private String width;
            private String height;
            private String weight;

            private List<StockInfo> stockInfoList;

            @Data
            public static class StockInfo {
                private Integer inventoryNum;
                private String supplierWarehouseId;
            }
        }
    }
}

