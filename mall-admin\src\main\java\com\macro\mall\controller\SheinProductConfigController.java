package com.macro.mall.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.Sheindto.SheinProductConfig;
import com.macro.mall.service.SheinProductConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * SHEIN商品配置管理Controller
 */
@Slf4j
@RestController
@Api(tags = "SheinProductConfigController", description = "SHEIN商品配置管理")
@RequestMapping("/shein/product/config")
public class SheinProductConfigController {

    @Resource
    private SheinProductConfigService sheinProductConfigService;

    @ApiOperation("创建商品配置")
    @PostMapping("/create")
    public CommonResult<SheinProductConfig> createConfig(@RequestBody SheinProductConfig config) {
        return sheinProductConfigService.createConfig(config);
    }

    @ApiOperation("根据商品ID查询配置")
    @GetMapping("/get/{productId}")
    public CommonResult<SheinProductConfig> getConfigByProductId(
            @ApiParam(value = "商品ID", required = true) @PathVariable Long productId) {
        return sheinProductConfigService.getConfigByProductId(productId);
    }

    @ApiOperation("根据商品ID更新配置")
    @PutMapping("/update/{productId}")
    public CommonResult<SheinProductConfig> updateConfigByProductId(
            @ApiParam(value = "商品ID", required = true) @PathVariable Long productId,
            @RequestBody SheinProductConfig config) {
        return sheinProductConfigService.updateConfigByProductId(productId, config);
    }

    @ApiOperation("根据商品ID删除配置")
    @DeleteMapping("/delete/{productId}")
    public CommonResult<String> deleteConfigByProductId(
            @ApiParam(value = "商品ID", required = true) @PathVariable Long productId) {
        return sheinProductConfigService.deleteConfigByProductId(productId);
    }

    @ApiOperation("分页查询配置列表")
    @GetMapping("/list")
    public CommonResult<List<SheinProductConfig>> listConfigs(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页数量", defaultValue = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        return sheinProductConfigService.listConfigs(pageNum, pageSize);
    }

    @ApiOperation("查询所有配置列表")
    @GetMapping("/all")
    public CommonResult<List<SheinProductConfig>> getAllConfigs() {
        return sheinProductConfigService.getAllConfigs();
    }
}
