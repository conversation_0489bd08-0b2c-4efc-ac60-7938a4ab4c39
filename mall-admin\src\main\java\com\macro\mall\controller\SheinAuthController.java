package com.macro.mall.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dto.SheinAuthCallbackDTO;
import com.macro.mall.dto.SheinAuthPrepareDTO;
import com.macro.mall.dto.SheinGetTokenDTO;
import com.macro.mall.service.SheinAuthService;
import com.macro.mall.util.ApiSignDemoUtil;
import com.macro.mall.vo.AuthResultVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/shein/auth")
public class SheinAuthController {

    @Autowired
    private SheinAuthService sheinAuthService;

    @ApiOperation(value = "获取 Shein 授权跳转链接")
    @GetMapping("/link")
    public CommonResult<SheinAuthPrepareDTO> getAuthLink() {
        return sheinAuthService.prepareAuthLink();
    }
    @ApiOperation("接收 Shein 授权回调")
    @GetMapping("/callback")
    public CommonResult handleSheinCallback(@RequestParam("appid") String appid,
                                            @RequestParam("tempToken") String tempToken,
                                            @RequestParam("state") String state) {
        SheinAuthCallbackDTO dto = new SheinAuthCallbackDTO();
        dto.setAppid(appid);
        dto.setTempToken(tempToken);
        dto.setState(state);
        return sheinAuthService.handleCallback(dto);
    }

}