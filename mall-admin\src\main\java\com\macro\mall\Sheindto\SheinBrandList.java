package com.macro.mall.Sheindto;

import lombok.Data;

import java.util.List;

/**
 * SHEIN品牌列表响应DTO
 */
@Data
public class SheinBrandList {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    @Data
    public static class Info {
        /**
         * 品牌数据列表
         */
        private List<Brand> data;
        
        /**
         * 元数据信息
         */
        private Meta meta;
    }
    
    @Data
    public static class Brand {
        /**
         * 品牌代码
         */
        private String brandCode;
        
        /**
         * 品牌名称
         */
        private String brandName;
    }
    
    @Data
    public static class Meta {
        /**
         * 总数量
         */
        private Integer count;
        
        /**
         * 自定义对象
         */
        private Object customObj;
    }
}
