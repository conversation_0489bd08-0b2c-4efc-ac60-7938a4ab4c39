<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SheinProductConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.macro.mall.model.SheinProductConfig">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="shein_category_id" property="sheinCategoryId" />
        <result column="shein_product_type_id" property="sheinProductTypeId" />
        <result column="SPU_attribute_id" property="spuAttributeId" />
        <result column="SKC_attribute_id" property="skcAttributeId" />
        <result column="main_site" property="mainSite" />
        <result column="sub_site_list" property="subSiteList" />
        <result column="image_url" property="imageUrl" />
        <result column="last_published_at" property="lastPublishedAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, shein_category_id, shein_product_type_id, SPU_attribute_id, SKC_attribute_id, 
        main_site, sub_site_list, image_url, last_published_at, created_at, updated_at
    </sql>

    <!-- 根据商品ID查询配置 -->
    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM shein_product_config
        WHERE product_id = #{productId}
    </select>

    <!-- 根据商品ID删除配置 -->
    <delete id="deleteByProductId">
        DELETE FROM shein_product_config WHERE product_id = #{productId}
    </delete>

    <!-- 根据商品ID更新配置 -->
    <update id="updateByProductId">
        UPDATE shein_product_config
        <set>
            <if test="config.sheinCategoryId != null">shein_category_id = #{config.sheinCategoryId},</if>
            <if test="config.sheinProductTypeId != null">shein_product_type_id = #{config.sheinProductTypeId},</if>
            <if test="config.spuAttributeId != null">SPU_attribute_id = #{config.spuAttributeId},</if>
            <if test="config.skcAttributeId != null">SKC_attribute_id = #{config.skcAttributeId},</if>
            <if test="config.mainSite != null and config.mainSite != ''">main_site = #{config.mainSite},</if>
            <if test="config.subSiteList != null and config.subSiteList != ''">sub_site_list = #{config.subSiteList},</if>
            <if test="config.imageUrl != null and config.imageUrl != ''">image_url = #{config.imageUrl},</if>
            <if test="config.lastPublishedAt != null and config.lastPublishedAt != ''">last_published_at = #{config.lastPublishedAt},</if>
            <if test="config.createdAt != null and config.createdAt != ''">created_at = #{config.createdAt},</if>
            updated_at = NOW()
        </set>
        WHERE product_id = #{config.productId}
    </update>

    <!-- 查询所有配置列表 -->
    <select id="selectAllConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM shein_product_config
        ORDER BY updated_at DESC
    </select>

    <!-- 分页查询配置列表 -->
    <select id="selectConfigsWithPaging" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM shein_product_config
        ORDER BY updated_at DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询配置总数 -->
    <select id="countConfigs" resultType="java.lang.Long">
        SELECT COUNT(*) FROM shein_product_config
    </select>

</mapper>
