2025-06-23 10:52:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-23 10:52:29 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 3780 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-23 10:52:29 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-23 10:52:29 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-23 10:52:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 10:52:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23 10:52:31 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 0 Redis repository interfaces.
2025-06-23 10:52:31 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$83bff66c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-23 10:52:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-23 10:52:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 10:52:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-23 10:52:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-23 10:52:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-23 10:52:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1891 ms
2025-06-23 10:52:31 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-23 10:52:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-23 10:52:44 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-23 10:52:44 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-23 10:52:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-23 10:52:45 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@59d6a4d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5b476930, org.springframework.security.web.context.SecurityContextPersistenceFilter@69eb1f23, org.springframework.security.web.header.HeaderWriterFilter@384cd83a, org.springframework.security.web.authentication.logout.LogoutFilter@63661fc7, com.macro.mall.security.component.JwtAuthenticationTokenFilter@76cbee13, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29897daf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63e54c66, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@18f1260, org.springframework.security.web.session.SessionManagementFilter@307eb95f, org.springframework.security.web.access.ExceptionTranslationFilter@52ec5ba6, com.macro.mall.security.component.DynamicSecurityFilter@394e504d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f80b25a]
2025-06-23 10:52:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 10:52:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 10:52:46 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-23 10:52:47 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 17.442 seconds (JVM running for 18.08)
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-23 10:53:29 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-23 10:53:29 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-23 10:53:29 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==>  Preparing: SELECT w.id AS warehouse_id, w.name, w.location FROM wms_admin_warehouse_relation awr JOIN wms_warehouse w ON awr.warehouse_id = w.id WHERE awr.admin_id = ?
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==> Parameters: 20(Long)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - <==      Total: 0
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 当前用户拥有的仓库 ID 列表: []
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 用户没有仓库权限，将跳过仓库权限过滤
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - ==>  Preparing: SELECT count(0) FROM pms_product WHERE (delete_status = ?)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - ==> Parameters: 0(Integer)
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - <==      Total: 1
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - ==>  Preparing: select id,name,product_sn,product_code,pic,sub_title,description,detail_title,detail_desc, detail_html,detail_mobile_html,brand_id,product_category_id,product_category_name,product_cate_uplevel_id, product_cate_uplevel_name,product_attribute_category_id,warehouse_id,feight_template_id,location,sort,sale, price,original_price,promotion_price,stock,low_stock,unit,height,length,width,size,weight,service_ids,keywords, note,album_pics,publish_status,verify_status,delete_status,new_status,recommand_status,preview_status,promotion_type, promotion_start_time,promotion_end_time,promotion_per_limit,gift_point,gift_growth,use_point_limit,shelf_time,brand_name,currency from pms_product WHERE ( delete_status = ? ) LIMIT ?
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - ==> Parameters: 0(Integer), 5(Integer)
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - <==      Total: 5
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 查询到符合条件的商品数量: 5
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2075, 商品名称: 一次性竹刀 , 商品编码: 2419784.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2076, 商品名称: 一次性竹勺 , 商品编码: 2419786.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2077, 商品名称: 雪糕棒, 商品编码: 2419788.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2078, 商品名称: 雪糕棒, 商品编码: 2419789.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2079, 商品名称: 蛋形球, 商品编码: 689255.0
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /product/list(String), GET(String), 查询商品(String), {"publishStatus":null,"verifyStatus":null,"keyword":null,"productSn":null,"productCode":null,"productCategoryId":null,"productCateUplevelId":null,"brandId":null,"minSize":null,"maxSize":null,"minWeight":null,"maxWeight":null,"warehouseId":null,"location":null,"minPrice":null,"maxPrice":null,"newStatus":null,"recommandStatus":null,"endMonth":null,"startMonth":null}(String), 0:0:0:0:0:0:0:1(String)
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品","username":"testls","startTime":1750647210548,"spendTime":803,"basePath":"http://localhost:8080","uri":"/product/list","url":"http://localhost:8080/product/list","method":"GET","ip":"*************","parameter":[{"pageSize":5},{"pageNum":1}],"result":{"code":200,"message":"操作成功","data":{"pageNum":1,"pageSize":5,"totalPage":7863,"total":39313,"list":[{"id":2075,"productSn":"1000087873","name":"一次性竹刀 ","pic":"https://img-eu-4.freex.es/img/2320/1000087873/600x600/290a21b7915542c087564b0debf8ada4","subTitle":"DRUT STALOWY","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419784.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2076,"productSn":"1000087875","name":"一次性竹勺 ","pic":"https://img-eu-4.freex.es/img/2320/1000087875/600x600/2c2c246fdb498f7191f3795767de5939","subTitle":"JEDNORAZOWA ŁYŻKA BAMBUSOWA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419786.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2078,"productSn":"1000087878","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087878/600x600/42f5cae5e1363098632009b62fbd48ea","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":4.35,"originalPrice":4.35,"stock":999,"productCode":"2419789.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2079,"productSn":"1000034879","name":"蛋形球","pic":"https://img-eu-4.freex.es/img/2320/1000034879/600x600/c5698fa744e9ce034c667b1f7c6664b7","subTitle":"PIŁKA W KSZTAŁCIE JAJKA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.2,"originalPrice":2.2,"stock":999,"productCode":"689255.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}]}}}
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==>  Preparing: SELECT w.id AS warehouse_id, w.name, w.location FROM wms_admin_warehouse_relation awr JOIN wms_warehouse w ON awr.warehouse_id = w.id WHERE awr.admin_id = ?
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==> Parameters: 20(Long)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - <==      Total: 0
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 当前用户拥有的仓库 ID 列表: []
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 用户没有仓库权限，将跳过仓库权限过滤
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - ==>  Preparing: SELECT count(0) FROM pms_product WHERE (delete_status = ?)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - ==> Parameters: 0(Integer)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - <==      Total: 1
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - ==>  Preparing: select id,name,product_sn,product_code,pic,sub_title,description,detail_title,detail_desc, detail_html,detail_mobile_html,brand_id,product_category_id,product_category_name,product_cate_uplevel_id, product_cate_uplevel_name,product_attribute_category_id,warehouse_id,feight_template_id,location,sort,sale, price,original_price,promotion_price,stock,low_stock,unit,height,length,width,size,weight,service_ids,keywords, note,album_pics,publish_status,verify_status,delete_status,new_status,recommand_status,preview_status,promotion_type, promotion_start_time,promotion_end_time,promotion_per_limit,gift_point,gift_growth,use_point_limit,shelf_time,brand_name,currency from pms_product WHERE ( delete_status = ? ) LIMIT ?
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - ==> Parameters: 0(Integer), 5(Integer)
2025-06-23 10:54:24 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - <==      Total: 5
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 查询到符合条件的商品数量: 5
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2075, 商品名称: 一次性竹刀 , 商品编码: 2419784.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2076, 商品名称: 一次性竹勺 , 商品编码: 2419786.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2077, 商品名称: 雪糕棒, 商品编码: 2419788.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2078, 商品名称: 雪糕棒, 商品编码: 2419789.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2079, 商品名称: 蛋形球, 商品编码: 689255.0
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /product/list(String), GET(String), 查询商品(String), {"publishStatus":null,"verifyStatus":null,"keyword":null,"productSn":null,"productCode":null,"productCategoryId":null,"productCateUplevelId":null,"brandId":null,"minSize":null,"maxSize":null,"minWeight":null,"maxWeight":null,"warehouseId":null,"location":null,"minPrice":null,"maxPrice":null,"newStatus":null,"recommandStatus":null,"endMonth":null,"startMonth":null}(String), 0:0:0:0:0:0:0:1(String)
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品","username":"testls","startTime":1750647263490,"spendTime":723,"basePath":"http://localhost:8080","uri":"/product/list","url":"http://localhost:8080/product/list","method":"GET","ip":"*************","parameter":[{"pageSize":5},{"pageNum":1}],"result":{"code":200,"message":"操作成功","data":{"pageNum":1,"pageSize":5,"totalPage":7863,"total":39313,"list":[{"id":2075,"productSn":"1000087873","name":"一次性竹刀 ","pic":"https://img-eu-4.freex.es/img/2320/1000087873/600x600/290a21b7915542c087564b0debf8ada4","subTitle":"DRUT STALOWY","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419784.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2076,"productSn":"1000087875","name":"一次性竹勺 ","pic":"https://img-eu-4.freex.es/img/2320/1000087875/600x600/2c2c246fdb498f7191f3795767de5939","subTitle":"JEDNORAZOWA ŁYŻKA BAMBUSOWA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419786.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2078,"productSn":"1000087878","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087878/600x600/42f5cae5e1363098632009b62fbd48ea","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":4.35,"originalPrice":4.35,"stock":999,"productCode":"2419789.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2079,"productSn":"1000034879","name":"蛋形球","pic":"https://img-eu-4.freex.es/img/2320/1000034879/600x600/c5698fa744e9ce034c667b1f7c6664b7","subTitle":"PIŁKA W KSZTAŁCIE JAJKA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.2,"originalPrice":2.2,"stock":999,"productCode":"689255.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}]}}}
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 12:57:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:57:38 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 134392 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 12:57:38 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 12:57:38 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103 ms. Found 0 Redis repository interfaces.
2025-06-25 12:57:48 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$505afe2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 12:57:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 12:57:48 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 12:57:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:57:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:57:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:57:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9781 ms
2025-06-25 12:57:48 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:57:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 12:58:00 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 12:58:00 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 12:58:01 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 12:58:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b2317b7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56c26b21, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ad5f99, org.springframework.security.web.header.HeaderWriterFilter@45ea6c24, org.springframework.security.web.authentication.logout.LogoutFilter@3b863b4, com.macro.mall.security.component.JwtAuthenticationTokenFilter@f8fe81e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@457dc809, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5687ba39, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a72517e, org.springframework.security.web.session.SessionManagementFilter@56f4f385, org.springframework.security.web.access.ExceptionTranslationFilter@3d72abea, com.macro.mall.security.component.DynamicSecurityFilter@3c34c491, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@15ff247b]
2025-06-25 12:58:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 12:58:03 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 12:58:03 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 25.181 seconds (JVM running for 25.916)
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 12:58:04 [boundedElastic-1] WARN  o.s.b.a.r.RedisReactiveHealthIndicator - Redis health check failed
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:1117)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:509)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:103)
	at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:86)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:227)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 14 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 12:58:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 12:58:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:58:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:58:28 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 20336 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 12:58:28 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 12:58:28 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 0 Redis repository interfaces.
2025-06-25 12:58:30 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$a844a24e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 12:58:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 12:58:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:58:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:58:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:58:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2140 ms
2025-06-25 12:58:30 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:58:37 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 12:58:39 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 12:58:39 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 12:58:40 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 12:58:40 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4583a186, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55854382, org.springframework.security.web.context.SecurityContextPersistenceFilter@475b796d, org.springframework.security.web.header.HeaderWriterFilter@6c89c92e, org.springframework.security.web.authentication.logout.LogoutFilter@5a35efac, com.macro.mall.security.component.JwtAuthenticationTokenFilter@5c43cb12, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69c7fb94, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@67c61551, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7843b65e, org.springframework.security.web.session.SessionManagementFilter@67544105, org.springframework.security.web.access.ExceptionTranslationFilter@33ccab9c, com.macro.mall.security.component.DynamicSecurityFilter@3c2fa57a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6fc84468]
2025-06-25 12:58:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 12:58:41 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 12:58:42 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 14.579 seconds (JVM running for 15.097)
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-25 12:58:49 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /]
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 12:59:21.027(Timestamp), ************(String), null, null
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 12:59:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 12:59:22 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827560322,"spendTime":1707,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NTYwNzk0LCJleHAiOjE3NTE0MzIzNjB9.1mpZTDQ55fCTNWaZxLc3snvVfKq6wuDPtYv7GzFJ3ZXCP6EkhNkzw6YYmJc_NSdI5O4GjBjPz3u6CUAUz59PEg"}}}
2025-06-25 13:00:22 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:00:30 [http-nio-8080-exec-8] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:00:45 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:00:45 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:00:45.617(Timestamp), ************(String), null, null
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827645525,"spendTime":1266,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NjQ1NjE2LCJleHAiOjE3NTE0MzI0NDV9.WOQXo-xSGEQTLMmGKLjtQ4P1qidmWcsbatuLvsiwP4EPtr8GXenKOvh4j8DcACN1bcZIth9_WyDnu94n71vMGQ"}}}
2025-06-25 13:01:12 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/publish-spec]
2025-06-25 13:01:24 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/publish-spec]
2025-06-25 13:01:54 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Failed to authorize filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-25 13:02:02 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:02:02 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:02:02.76(Timestamp), ************(String), null, null
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827722667,"spendTime":1295,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NzIyNzU5LCJleHAiOjE3NTE0MzI1MjJ9.fBxlh8bSDADgQ7ij78Mld9GzTi8kuaM4im19Mhj8RwfR6U6mgrt-n2onqZi6p8gUI-lFWhptO23jn_RazpQBHw"}}}
2025-06-25 13:02:23 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Failed to authorize filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-25 13:03:01 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:03:13 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
