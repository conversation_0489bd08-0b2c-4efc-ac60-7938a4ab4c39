2025-06-23 10:52:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-23 10:52:29 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 3780 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-23 10:52:29 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-23 10:52:29 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-23 10:52:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 10:52:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23 10:52:31 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 0 Redis repository interfaces.
2025-06-23 10:52:31 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$83bff66c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-23 10:52:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-23 10:52:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 10:52:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-23 10:52:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-23 10:52:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-23 10:52:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1891 ms
2025-06-23 10:52:31 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-23 10:52:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-23 10:52:44 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-23 10:52:44 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-23 10:52:44 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-23 10:52:45 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-23 10:52:45 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@59d6a4d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5b476930, org.springframework.security.web.context.SecurityContextPersistenceFilter@69eb1f23, org.springframework.security.web.header.HeaderWriterFilter@384cd83a, org.springframework.security.web.authentication.logout.LogoutFilter@63661fc7, com.macro.mall.security.component.JwtAuthenticationTokenFilter@76cbee13, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29897daf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63e54c66, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@18f1260, org.springframework.security.web.session.SessionManagementFilter@307eb95f, org.springframework.security.web.access.ExceptionTranslationFilter@52ec5ba6, com.macro.mall.security.component.DynamicSecurityFilter@394e504d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f80b25a]
2025-06-23 10:52:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 10:52:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 10:52:46 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-23 10:52:47 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 17.442 seconds (JVM running for 18.08)
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-23 10:52:47 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-23 10:53:29 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-23 10:53:29 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-23 10:53:29 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==>  Preparing: SELECT w.id AS warehouse_id, w.name, w.location FROM wms_admin_warehouse_relation awr JOIN wms_warehouse w ON awr.warehouse_id = w.id WHERE awr.admin_id = ?
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==> Parameters: 20(Long)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - <==      Total: 0
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 当前用户拥有的仓库 ID 列表: []
2025-06-23 10:53:30 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 用户没有仓库权限，将跳过仓库权限过滤
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - ==>  Preparing: SELECT count(0) FROM pms_product WHERE (delete_status = ?)
2025-06-23 10:53:30 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - ==> Parameters: 0(Integer)
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample_COUNT - <==      Total: 1
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - ==>  Preparing: select id,name,product_sn,product_code,pic,sub_title,description,detail_title,detail_desc, detail_html,detail_mobile_html,brand_id,product_category_id,product_category_name,product_cate_uplevel_id, product_cate_uplevel_name,product_attribute_category_id,warehouse_id,feight_template_id,location,sort,sale, price,original_price,promotion_price,stock,low_stock,unit,height,length,width,size,weight,service_ids,keywords, note,album_pics,publish_status,verify_status,delete_status,new_status,recommand_status,preview_status,promotion_type, promotion_start_time,promotion_end_time,promotion_per_limit,gift_point,gift_growth,use_point_limit,shelf_time,brand_name,currency from pms_product WHERE ( delete_status = ? ) LIMIT ?
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - ==> Parameters: 0(Integer), 5(Integer)
2025-06-23 10:53:31 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByExample - <==      Total: 5
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 查询到符合条件的商品数量: 5
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2075, 商品名称: 一次性竹刀 , 商品编码: 2419784.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2076, 商品名称: 一次性竹勺 , 商品编码: 2419786.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2077, 商品名称: 雪糕棒, 商品编码: 2419788.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2078, 商品名称: 雪糕棒, 商品编码: 2419789.0
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2079, 商品名称: 蛋形球, 商品编码: 689255.0
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /product/list(String), GET(String), 查询商品(String), {"publishStatus":null,"verifyStatus":null,"keyword":null,"productSn":null,"productCode":null,"productCategoryId":null,"productCateUplevelId":null,"brandId":null,"minSize":null,"maxSize":null,"minWeight":null,"maxWeight":null,"warehouseId":null,"location":null,"minPrice":null,"maxPrice":null,"newStatus":null,"recommandStatus":null,"endMonth":null,"startMonth":null}(String), 0:0:0:0:0:0:0:1(String)
2025-06-23 10:53:31 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品","username":"testls","startTime":1750647210548,"spendTime":803,"basePath":"http://localhost:8080","uri":"/product/list","url":"http://localhost:8080/product/list","method":"GET","ip":"*************","parameter":[{"pageSize":5},{"pageNum":1}],"result":{"code":200,"message":"操作成功","data":{"pageNum":1,"pageSize":5,"totalPage":7863,"total":39313,"list":[{"id":2075,"productSn":"1000087873","name":"一次性竹刀 ","pic":"https://img-eu-4.freex.es/img/2320/1000087873/600x600/290a21b7915542c087564b0debf8ada4","subTitle":"DRUT STALOWY","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419784.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2076,"productSn":"1000087875","name":"一次性竹勺 ","pic":"https://img-eu-4.freex.es/img/2320/1000087875/600x600/2c2c246fdb498f7191f3795767de5939","subTitle":"JEDNORAZOWA ŁYŻKA BAMBUSOWA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419786.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2078,"productSn":"1000087878","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087878/600x600/42f5cae5e1363098632009b62fbd48ea","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":4.35,"originalPrice":4.35,"stock":999,"productCode":"2419789.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2079,"productSn":"1000034879","name":"蛋形球","pic":"https://img-eu-4.freex.es/img/2320/1000034879/600x600/c5698fa744e9ce034c667b1f7c6664b7","subTitle":"PIŁKA W KSZTAŁCIE JAJKA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.2,"originalPrice":2.2,"stock":999,"productCode":"689255.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}]}}}
2025-06-23 10:53:31 [task-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==>  Preparing: SELECT w.id AS warehouse_id, w.name, w.location FROM wms_admin_warehouse_relation awr JOIN wms_warehouse w ON awr.warehouse_id = w.id WHERE awr.admin_id = ?
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - ==> Parameters: 20(Long)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.selectWarehouseIdsByAdminId - <==      Total: 0
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 当前用户拥有的仓库 ID 列表: []
2025-06-23 10:54:23 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 用户没有仓库权限，将跳过仓库权限过滤
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - ==>  Preparing: SELECT count(0) FROM pms_product WHERE (delete_status = ?)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - ==> Parameters: 0(Integer)
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample_COUNT - <==      Total: 1
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - ==>  Preparing: select id,name,product_sn,product_code,pic,sub_title,description,detail_title,detail_desc, detail_html,detail_mobile_html,brand_id,product_category_id,product_category_name,product_cate_uplevel_id, product_cate_uplevel_name,product_attribute_category_id,warehouse_id,feight_template_id,location,sort,sale, price,original_price,promotion_price,stock,low_stock,unit,height,length,width,size,weight,service_ids,keywords, note,album_pics,publish_status,verify_status,delete_status,new_status,recommand_status,preview_status,promotion_type, promotion_start_time,promotion_end_time,promotion_per_limit,gift_point,gift_growth,use_point_limit,shelf_time,brand_name,currency from pms_product WHERE ( delete_status = ? ) LIMIT ?
2025-06-23 10:54:23 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - ==> Parameters: 0(Integer), 5(Integer)
2025-06-23 10:54:24 [http-nio-8080-exec-4] DEBUG c.m.m.m.P.selectByExample - <==      Total: 5
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 查询到符合条件的商品数量: 5
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2075, 商品名称: 一次性竹刀 , 商品编码: 2419784.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2076, 商品名称: 一次性竹勺 , 商品编码: 2419786.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2077, 商品名称: 雪糕棒, 商品编码: 2419788.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2078, 商品名称: 雪糕棒, 商品编码: 2419789.0
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.m.m.s.impl.PmsProductServiceImpl - 商品ID: 2079, 商品名称: 蛋形球, 商品编码: 689255.0
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /product/list(String), GET(String), 查询商品(String), {"publishStatus":null,"verifyStatus":null,"keyword":null,"productSn":null,"productCode":null,"productCategoryId":null,"productCateUplevelId":null,"brandId":null,"minSize":null,"maxSize":null,"minWeight":null,"maxWeight":null,"warehouseId":null,"location":null,"minPrice":null,"maxPrice":null,"newStatus":null,"recommandStatus":null,"endMonth":null,"startMonth":null}(String), 0:0:0:0:0:0:0:1(String)
2025-06-23 10:54:24 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品","username":"testls","startTime":1750647263490,"spendTime":723,"basePath":"http://localhost:8080","uri":"/product/list","url":"http://localhost:8080/product/list","method":"GET","ip":"*************","parameter":[{"pageSize":5},{"pageNum":1}],"result":{"code":200,"message":"操作成功","data":{"pageNum":1,"pageSize":5,"totalPage":7863,"total":39313,"list":[{"id":2075,"productSn":"1000087873","name":"一次性竹刀 ","pic":"https://img-eu-4.freex.es/img/2320/1000087873/600x600/290a21b7915542c087564b0debf8ada4","subTitle":"DRUT STALOWY","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419784.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2076,"productSn":"1000087875","name":"一次性竹勺 ","pic":"https://img-eu-4.freex.es/img/2320/1000087875/600x600/2c2c246fdb498f7191f3795767de5939","subTitle":"JEDNORAZOWA ŁYŻKA BAMBUSOWA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":3,"originalPrice":3,"stock":999,"productCode":"2419786.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2078,"productSn":"1000087878","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087878/600x600/42f5cae5e1363098632009b62fbd48ea","subTitle":"PATYCZKI DO LOD覹","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":4.35,"originalPrice":4.35,"stock":999,"productCode":"2419789.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"},{"id":2079,"productSn":"1000034879","name":"蛋形球","pic":"https://img-eu-4.freex.es/img/2320/1000034879/600x600/c5698fa744e9ce034c667b1f7c6664b7","subTitle":"PIŁKA W KSZTAŁCIE JAJKA","productCategoryId":649,"productCategoryName":"Z NOWA DOSTAWA 最新到货","productCateUplevelId":57,"productCateUplevelName":"Z NOWA DOSTAWA 最新到货","warehouseId":1,"location":"Poland","price":2.2,"originalPrice":2.2,"stock":999,"productCode":"689255.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}]}}}
2025-06-23 10:54:24 [task-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 12:57:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:57:38 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 134392 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 12:57:38 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 12:57:38 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:57:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103 ms. Found 0 Redis repository interfaces.
2025-06-25 12:57:48 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$505afe2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 12:57:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 12:57:48 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 12:57:48 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 12:57:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:57:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:57:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:57:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9781 ms
2025-06-25 12:57:48 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:57:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 12:58:00 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 12:58:00 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 12:58:00 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 12:58:01 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 12:58:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b2317b7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56c26b21, org.springframework.security.web.context.SecurityContextPersistenceFilter@3ad5f99, org.springframework.security.web.header.HeaderWriterFilter@45ea6c24, org.springframework.security.web.authentication.logout.LogoutFilter@3b863b4, com.macro.mall.security.component.JwtAuthenticationTokenFilter@f8fe81e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@457dc809, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5687ba39, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a72517e, org.springframework.security.web.session.SessionManagementFilter@56f4f385, org.springframework.security.web.access.ExceptionTranslationFilter@3d72abea, com.macro.mall.security.component.DynamicSecurityFilter@3c34c491, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@15ff247b]
2025-06-25 12:58:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 12:58:03 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 12:58:03 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 25.181 seconds (JVM running for 25.916)
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:58:04 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 12:58:04 [boundedElastic-1] WARN  o.s.b.a.r.RedisReactiveHealthIndicator - Redis health check failed
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:1117)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:509)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:103)
	at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:86)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:227)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 14 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 12:58:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 12:58:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:58:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:58:28 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 20336 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 12:58:28 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 12:58:28 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:58:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 0 Redis repository interfaces.
2025-06-25 12:58:30 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$a844a24e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 12:58:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 12:58:30 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 12:58:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:58:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:58:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:58:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2140 ms
2025-06-25 12:58:30 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:58:37 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 12:58:39 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 12:58:39 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 12:58:39 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 12:58:40 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 12:58:40 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4583a186, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55854382, org.springframework.security.web.context.SecurityContextPersistenceFilter@475b796d, org.springframework.security.web.header.HeaderWriterFilter@6c89c92e, org.springframework.security.web.authentication.logout.LogoutFilter@5a35efac, com.macro.mall.security.component.JwtAuthenticationTokenFilter@5c43cb12, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69c7fb94, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@67c61551, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7843b65e, org.springframework.security.web.session.SessionManagementFilter@67544105, org.springframework.security.web.access.ExceptionTranslationFilter@33ccab9c, com.macro.mall.security.component.DynamicSecurityFilter@3c2fa57a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6fc84468]
2025-06-25 12:58:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 12:58:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 12:58:41 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 12:58:42 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 14.579 seconds (JVM running for 15.097)
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:58:42 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-25 12:58:49 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /]
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-25 12:59:20 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 12:59:21.027(Timestamp), ************(String), null, null
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 12:59:21 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 12:59:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 12:59:22 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827560322,"spendTime":1707,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NTYwNzk0LCJleHAiOjE3NTE0MzIzNjB9.1mpZTDQ55fCTNWaZxLc3snvVfKq6wuDPtYv7GzFJ3ZXCP6EkhNkzw6YYmJc_NSdI5O4GjBjPz3u6CUAUz59PEg"}}}
2025-06-25 13:00:22 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:00:30 [http-nio-8080-exec-8] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:00:45 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:00:45 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:00:45.617(Timestamp), ************(String), null, null
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:00:46 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:00:46 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827645525,"spendTime":1266,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NjQ1NjE2LCJleHAiOjE3NTE0MzI0NDV9.WOQXo-xSGEQTLMmGKLjtQ4P1qidmWcsbatuLvsiwP4EPtr8GXenKOvh4j8DcACN1bcZIth9_WyDnu94n71vMGQ"}}}
2025-06-25 13:01:12 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/publish-spec]
2025-06-25 13:01:24 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/publish-spec]
2025-06-25 13:01:54 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Failed to authorize filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-25 13:02:02 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:02:02 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:02:02.76(Timestamp), ************(String), null, null
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:02:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:02:03 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750827722667,"spendTime":1295,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI3NzIyNzU5LCJleHAiOjE3NTE0MzI1MjJ9.fBxlh8bSDADgQ7ij78Mld9GzTi8kuaM4im19Mhj8RwfR6U6mgrt-n2onqZi6p8gUI-lFWhptO23jn_RazpQBHw"}}}
2025-06-25 13:02:23 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Failed to authorize filter invocation [GET /product/list?pageNum=1&pageSize=5] with attributes [5:商品管理]
2025-06-25 13:03:01 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:03:13 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /shein/product/attribute-template]
2025-06-25 13:11:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 13:11:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 13:11:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 13:11:24 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 138960 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 13:11:24 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 13:11:24 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 13:11:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:11:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 13:11:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 88 ms. Found 0 Redis repository interfaces.
2025-06-25 13:11:25 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$7d0c33ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 13:11:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 13:11:26 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 13:11:26 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 13:11:26 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 13:11:26 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 13:11:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 13:11:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 13:11:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 13:11:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 13:11:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2304 ms
2025-06-25 13:11:26 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 13:11:35 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 13:11:36 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 13:11:36 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 13:11:37 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 13:11:37 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 13:11:37 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 13:11:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 13:11:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@56c26b21, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a72517e, org.springframework.security.web.context.SecurityContextPersistenceFilter@1af29a19, org.springframework.security.web.header.HeaderWriterFilter@5687ba39, org.springframework.security.web.authentication.logout.LogoutFilter@32aa27a7, com.macro.mall.security.component.JwtAuthenticationTokenFilter@1b881f1f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23b02f37, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@73cdf5f9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@77a14911, org.springframework.security.web.session.SessionManagementFilter@523e019c, org.springframework.security.web.access.ExceptionTranslationFilter@72b4ecb2, com.macro.mall.security.component.DynamicSecurityFilter@160cf225, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@71979130]
2025-06-25 13:11:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 13:11:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 13:11:39 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 13:11:40 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 16.537 seconds (JVM running for 17.121)
2025-06-25 13:11:40 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 13:11:40 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 13:11:40 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 13:12:02 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:12:02 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:12:02.533(Timestamp), ************(String), null, null
2025-06-25 13:12:02 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:12:02 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:12:02 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:12:03 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:12:03 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750828322171,"spendTime":1602,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI4MzIyMzAyLCJleHAiOjE3NTE0MzMxMjJ9.XVdn2JJ2AncL-PwUqgbEzK2BbkPbnGyLSQVX59SLy6NFwTfcWyMhhQBgMQZrSToXhe2Ms6EO4poN5U6g3kiFKQ"}}}
2025-06-25 13:12:28 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取商品发布规范","startTime":1750828348639,"spendTime":3,"basePath":"http://localhost:8080","uri":"/shein/product/publish-spec","url":"http://localhost:8080/shein/product/publish-spec","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"fillInStandardList":[{"module":"basic_info","fieldKey":"product_name","required":true,"show":true},{"module":"basic_info","fieldKey":"product_description","required":true,"show":true}],"defaultLanguage":"en","pictureConfigList":[{"pictureType":"main_image","minCount":1,"maxCount":10,"requirement":"主图要求：尺寸不小于800x800像素"}],"currency":"USD","supportSaleAttributeSort":true},"bbl":"test_bbl","traceId":"trace_1750828348642"}}
2025-06-25 13:13:18 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取可用品牌列表","startTime":1750828398982,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/brands","url":"http://localhost:8080/shein/product/brands","method":"POST","ip":"************","result":{"code":"0","msg":"success","info":{"data":[{"brandCode":"SHEIN","brandName":"SHEIN"},{"brandCode":"ROMWE","brandName":"ROMWE"},{"brandCode":"DAZY","brandName":"DAZY"}],"meta":{"count":3}},"bbl":"test_bbl"}}
2025-06-25 13:13:56 [http-nio-8080-exec-10] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加自定义属性值","startTime":1750828436251,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/add-custom-attribute-value","url":"http://localhost:8080/shein/product/add-custom-attribute-value","method":"POST","ip":"************","parameter":{"attributeId":27,"attributeValue":"Navy/Bittersweet/Peacoat","categoryId":4455},"result":{"code":"0","msg":"success","info":{"supplierId":12345,"supplierSource":1,"categoryId":4455,"attributeId":27,"attributeValueId":1750828436251,"attributeValueName":"Navy/Bittersweet/Peacoat"},"bbl":"test_bbl","traceId":"trace_1750828436251"}}
2025-06-25 13:14:29 [http-nio-8080-exec-8] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取类目树","startTime":1750828469900,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/categories","url":"http://localhost:8080/shein/product/categories","method":"POST","ip":"************","result":{"code":"0","msg":"success","info":{"data":[{"categoryId":1001,"productTypeId":2001,"parentCategoryId":0,"categoryName":"服装","lastCategory":false,"children":[{"categoryId":1002,"productTypeId":2002,"parentCategoryId":1001,"categoryName":"女装","lastCategory":false,"children":[{"categoryId":1003,"productTypeId":2003,"parentCategoryId":1002,"categoryName":"连衣裙","lastCategory":true},{"categoryId":1004,"productTypeId":2004,"parentCategoryId":1002,"categoryName":"上衣","lastCategory":true}]},{"categoryId":1005,"productTypeId":2005,"parentCategoryId":1001,"categoryName":"男装","lastCategory":false,"children":[{"categoryId":1006,"productTypeId":2006,"parentCategoryId":1005,"categoryName":"衬衫","lastCategory":true}]}]},{"categoryId":2001,"productTypeId":3001,"parentCategoryId":0,"categoryName":"配饰","lastCategory":false,"children":[{"categoryId":2002,"productTypeId":3002,"parentCategoryId":2001,"categoryName":"包包","lastCategory":true}]}],"meta":{"count":2}},"bbl":"test_bbl"}}
2025-06-25 13:14:38 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询是否支持自定义属性值","startTime":1750828478900,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/custom-attribute-permission","url":"http://localhost:8080/shein/product/custom-attribute-permission","method":"POST","ip":"************","parameter":{"categoryIdList":[0]},"result":{"code":"0","msg":"success","info":{"data":[{"hasPermission":1,"lastCategoryId":0,"attributeId":1001},{"hasPermission":0,"lastCategoryId":0,"attributeId":1002}],"meta":{"count":2}},"bbl":"test_bbl","traceId":"trace_1750828478900"}}
2025-06-25 13:14:48 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可售站点","startTime":1750828488222,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/site-list","url":"http://localhost:8080/shein/product/site-list","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"data":[{"mainSite":"US","mainSiteName":"美国","subSiteList":[{"siteName":"美国","siteAbbr":"US","siteStatus":1,"storeType":1}]},{"mainSite":"EU","mainSiteName":"欧洲","subSiteList":[{"siteName":"德国","siteAbbr":"DE","siteStatus":1,"storeType":1},{"siteName":"法国","siteAbbr":"FR","siteStatus":1,"storeType":1},{"siteName":"意大利","siteAbbr":"IT","siteStatus":1,"storeType":1}]},{"mainSite":"ASIA","mainSiteName":"亚洲","subSiteList":[{"siteName":"日本","siteAbbr":"JP","siteStatus":1,"storeType":1},{"siteName":"韩国","siteAbbr":"KR","siteStatus":1,"storeType":1}]}],"meta":{"count":3}},"bbl":"test_bbl","traceId":"trace_1750828488222"}}
2025-06-25 13:15:20 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750828520637,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750828520637.jpg"},"bbl":"test_bbl","traceId":"trace_1750828520637"}}
2025-06-25 13:15:51 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750828551460,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750828551460.jpg"},"bbl":"test_bbl","traceId":"trace_1750828551460"}}
2025-06-25 13:17:29 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-25 13:17:29 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: string(String)
2025-06-25 13:17:29 [http-nio-8080-exec-2] DEBUG c.m.m.m.U.selectByExample - <==      Total: 0
2025-06-25 13:17:29 [http-nio-8080-exec-2] WARN  c.m.m.s.impl.UmsAdminServiceImpl - 登录异常:用户名或密码错误
2025-06-25 13:17:29 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","startTime":1750828649423,"spendTime":427,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"string","password":"string"},"result":{"code":404,"message":"用户名或密码错误"}}
2025-06-25 13:17:36 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:17:36 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:17:36.824(Timestamp), ************(String), null, null
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:17:37 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:17:38 [http-nio-8080-exec-7] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:17:38 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750828656741,"spendTime":1310,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI4NjU2ODIyLCJleHAiOjE3NTE0MzM0NTZ9.MFrrsG8NtcZODMPne5q67eA3Mv4n6cj4w8Kr-ZMHX9mlsS_a8hwbz8mGiYiczbx5zRT19rWeHFEhkp_3fG5QoQ"}}}
2025-06-25 13:17:50 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750828670294,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750828670294.jpg"},"bbl":"test_bbl","traceId":"trace_1750828670294"}}
2025-06-25 13:18:05 [http-nio-8080-exec-8] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.web.multipart.MultipartException: Current request is not a multipart request] with root cause
org.springframework.web.multipart.MultipartException: Current request is not a multipart request
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:210)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:193)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:114)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:18:05 [http-nio-8080-exec-8] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error?image_type=2]
2025-06-25 13:19:17 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加自定义属性值","startTime":1750828757092,"spendTime":1,"basePath":"http://localhost:8080","uri":"/shein/product/add-custom-attribute-value","url":"http://localhost:8080/shein/product/add-custom-attribute-value","method":"POST","ip":"************","parameter":{"attributeId":27,"attributeValue":"Navy/Bittersweet/Peacoat","categoryId":4455},"result":{"code":"0","msg":"success","info":{"supplierId":12345,"supplierSource":1,"categoryId":4455,"attributeId":27,"attributeValueId":1750828757092,"attributeValueName":"Navy/Bittersweet/Peacoat"},"bbl":"test_bbl","traceId":"trace_1750828757092"}}
2025-06-25 13:20:02 [http-nio-8080-exec-9] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:20:02 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:20:27 [http-nio-8080-exec-5] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:20:27 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:20:30 [http-nio-8080-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:20:30 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:20:45 [http-nio-8080-exec-7] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor292.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:20:45 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:21:09 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取可用品牌列表","startTime":1750828869099,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/brands","url":"http://localhost:8080/shein/product/brands","method":"POST","ip":"************","result":{"code":"0","msg":"success","info":{"data":[{"brandCode":"SHEIN","brandName":"SHEIN"},{"brandCode":"ROMWE","brandName":"ROMWE"},{"brandCode":"DAZY","brandName":"DAZY"}],"meta":{"count":3}},"bbl":"test_bbl"}}
2025-06-25 13:21:18 [http-nio-8080-exec-8] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor292.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:21:18 [http-nio-8080-exec-8] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:21:26 [http-nio-8080-exec-10] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor292.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:21:26 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:21:56 [http-nio-8080-exec-4] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor292.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:21:56 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:22:19 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:22:19 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:22:19.309(Timestamp), ************(String), null, null
2025-06-25 13:22:19 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:22:19 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:22:19 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:22:20 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:22:20 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750828939215,"spendTime":1552,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODI4OTM5MzA2LCJleHAiOjE3NTE0MzM3Mzl9.X3VUKzgEroQElOQNwt6XBY55RLduhrf9NFHGVgIF7xKcgYMjK4OKb_CGNxs7El1jCGCNGhJC9t7P8TIkZsDPmg"}}}
2025-06-25 13:22:30 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可选属性","startTime":1750828950818,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/attribute-template","url":"http://localhost:8080/shein/product/attribute-template","method":"POST","ip":"************","parameter":{"productTypeIdList":[0]},"result":{"code":"0","msg":"success","info":{"data":[{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":0}],"meta":{"count":1}},"bbl":"test_bbl","traceId":"trace_1750828950818"}}
2025-06-25 13:22:45 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可选属性","startTime":1750828965204,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/attribute-template","url":"http://localhost:8080/shein/product/attribute-template","method":"POST","ip":"************","parameter":{"productTypeIdList":[2512]},"result":{"code":"0","msg":"success","info":{"data":[{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":2512}],"meta":{"count":1}},"bbl":"test_bbl","traceId":"trace_1750828965204"}}
2025-06-25 13:22:46 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可选属性","startTime":1750828966597,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/attribute-template","url":"http://localhost:8080/shein/product/attribute-template","method":"POST","ip":"************","parameter":{"productTypeIdList":[2512]},"result":{"code":"0","msg":"success","info":{"data":[{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":2512}],"meta":{"count":1}},"bbl":"test_bbl","traceId":"trace_1750828966597"}}
2025-06-25 13:23:27 [http-nio-8080-exec-10] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.util.List.iterator()" because the return value of "com.macro.mall.Sheindto.SheinAttributeTemplateRequest.getProductTypeIdList()" is null
	at com.macro.mall.service.impl.SheinProductServiceImpl.getAttributeTemplate(SheinProductServiceImpl.java:303)
	at com.macro.mall.controller.SheinProductController.getAttributeTemplate(SheinProductController.java:56)
	at com.macro.mall.controller.SheinProductController$$FastClassBySpringCGLIB$$e7e4a99d.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at jdk.internal.reflect.GeneratedMethodAccessor292.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.SheinProductController$$EnhancerBySpringCGLIB$$a3b77849.getAttributeTemplate(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:50)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 13:23:27 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 13:23:44 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可选属性","startTime":1750829024333,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/attribute-template","url":"http://localhost:8080/shein/product/attribute-template","method":"POST","ip":"************","parameter":{"productTypeIdList":[2512]},"result":{"code":"0","msg":"success","info":{"data":[{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":2512}],"meta":{"count":1}},"bbl":"test_bbl","traceId":"trace_1750829024333"}}
2025-06-25 13:24:21 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取商品发布规范","startTime":1750829061394,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/publish-spec","url":"http://localhost:8080/shein/product/publish-spec","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"fillInStandardList":[{"module":"basic_info","fieldKey":"product_name","required":true,"show":true},{"module":"basic_info","fieldKey":"product_description","required":true,"show":true}],"defaultLanguage":"en","pictureConfigList":[{"pictureType":"main_image","minCount":1,"maxCount":10,"requirement":"主图要求：尺寸不小于800x800像素"}],"currency":"USD","supportSaleAttributeSort":true},"bbl":"test_bbl","traceId":"trace_1750829061394"}}
2025-06-25 13:39:32 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750829972839,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750829972839.jpg"},"bbl":"test_bbl","traceId":"trace_1750829972839"}}
2025-06-25 13:45:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 13:45:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 13:46:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 13:46:15 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 136796 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 13:46:15 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 13:46:15 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 13:46:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:46:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 13:46:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 94 ms. Found 0 Redis repository interfaces.
2025-06-25 13:46:17 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$9bcd9ae8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 13:46:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 13:46:17 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 13:46:17 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 13:46:17 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 13:46:17 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 13:46:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 13:46:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 13:46:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 13:46:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 13:46:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2397 ms
2025-06-25 13:46:18 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 13:46:26 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 13:46:27 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 13:46:27 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 13:46:27 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 13:46:27 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 13:46:27 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 13:46:29 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 13:46:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@76fdd5f1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@513fb873, org.springframework.security.web.context.SecurityContextPersistenceFilter@18a349cb, org.springframework.security.web.header.HeaderWriterFilter@58e9e852, org.springframework.security.web.authentication.logout.LogoutFilter@2e214d39, com.macro.mall.security.component.JwtAuthenticationTokenFilter@59d09ff3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6012bee8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60b8c05c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2a4a95c4, org.springframework.security.web.session.SessionManagementFilter@57f83dc7, org.springframework.security.web.access.ExceptionTranslationFilter@5a35efac, com.macro.mall.security.component.DynamicSecurityFilter@62ff14cd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4407f129]
2025-06-25 13:46:29 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 13:46:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 13:46:30 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 13:46:30 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 15.556 seconds (JVM running for 16.108)
2025-06-25 13:46:31 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 13:46:31 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 13:46:31 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-25 13:46:57 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 13:46:57 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 13:46:57.594(Timestamp), ************(String), null, null
2025-06-25 13:46:57 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 13:46:57 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 13:46:57 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 13:46:58 [http-nio-8080-exec-4] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 13:46:58 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750830417266,"spendTime":1333,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODMwNDE3Mzg3LCJleHAiOjE3NTE0MzUyMTd9.LgGmdlg0s3ZkvoXqFRsGTZ-YtyUfAVwOLE3NgXBhBIHeAI10CBskVvYyx-ZnNDbtCrEc_XDIj5b7JGoL9Kc6gg"}}}
2025-06-25 13:47:07 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750830427553,"spendTime":3,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{"imageType":0,"originalUrl":"string"},"result":{"code":"-1","msg":"图片类型无效，支持的类型：1(主图), 2(细节图), 5(方块图), 6(色块图), 7(详情图)","traceId":"trace_1750830427556"}}
2025-06-25 13:47:12 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750830432677,"spendTime":1,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{"imageType":2,"originalUrl":"string"},"result":{"code":"0","msg":"success","info":{"original":"string","transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750830432677.jpg"},"bbl":"test_bbl","traceId":"trace_1750830432677"}}
2025-06-25 13:47:18 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750830438910,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{},"result":{"code":"-1","msg":"图片类型不能为空","traceId":"trace_1750830438910"}}
2025-06-25 13:48:03 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750830483514,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{"imageType":2,"originalUrl":"string"},"result":{"code":"0","msg":"success","info":{"original":"string","transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750830483514.jpg"},"bbl":"test_bbl","traceId":"trace_1750830483514"}}
2025-06-25 13:50:57 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加自定义属性值","startTime":1750830657012,"spendTime":1,"basePath":"http://localhost:8080","uri":"/shein/product/add-custom-attribute-value","url":"http://localhost:8080/shein/product/add-custom-attribute-value","method":"POST","ip":"************","parameter":{"attributeId":0,"attributeValue":"string","categoryId":0,"attributeValueNameMultis":[{"language":"string","attributeValueNameMulti":"string"}]},"result":{"code":"0","msg":"success","info":{"supplierId":12345,"supplierSource":1,"categoryId":0,"attributeId":0,"attributeValueId":1750830657013,"attributeValueName":"string","attributeValueMultiArr":[{"attributeValueNameMulti":"string","language":"string"}]},"bbl":"test_bbl","traceId":"trace_1750830657013"}}
2025-06-25 13:51:37 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 13:51:37 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:05:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:05:33 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 135920 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-25 14:05:33 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 14:05:33 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 14:05:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:05:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:05:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 91 ms. Found 0 Redis repository interfaces.
2025-06-25 14:05:35 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$1870e5b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 14:05:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 14:05:35 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-25 14:05:35 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-25 14:05:35 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-25 14:05:35 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-25 14:05:35 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 14:05:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:05:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:05:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:05:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2326 ms
2025-06-25 14:05:36 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:05:44 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:05:45 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 14:05:45 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 14:05:45 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 14:05:45 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 14:05:46 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 14:05:47 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 14:05:47 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@513fb873, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a4a95c4, org.springframework.security.web.context.SecurityContextPersistenceFilter@ea3491d, org.springframework.security.web.header.HeaderWriterFilter@52238861, org.springframework.security.web.authentication.logout.LogoutFilter@622e39d, com.macro.mall.security.component.JwtAuthenticationTokenFilter@2de07c57, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@60b8c05c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62d72091, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@71ae1cb, org.springframework.security.web.session.SessionManagementFilter@75937998, org.springframework.security.web.access.ExceptionTranslationFilter@70d24586, com.macro.mall.security.component.DynamicSecurityFilter@48a21ea6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@731bdcca]
2025-06-25 14:05:48 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 14:05:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 14:05:48 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 14:05:48 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 15.716 seconds (JVM running for 16.276)
2025-06-25 14:05:49 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:05:49 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:05:49 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 14:06:09.142(Timestamp), ************(String), null, null
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 14:06:09 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 14:06:10 [http-nio-8080-exec-3] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 14:06:10 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750831568781,"spendTime":1345,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODMxNTY4OTE3LCJleHAiOjE3NTE0MzYzNjh9.n8sHyBx7RdxaN1ZDv2Jb1EXaGcNjIcRCZf7_XrNbHy1Gg--ipB7wQ7bTZVexxaZjl0Yc6hno6ycWvqVzhURg0Q"}}}
2025-06-25 14:06:18 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商家仓库列表(自主运营和半托管模式)","startTime":1750831578333,"spendTime":3,"basePath":"http://localhost:8080","uri":"/shein/product/warehouse-list","url":"http://localhost:8080/shein/product/warehouse-list","method":"GET","ip":"************","result":{"code":"0","msg":"OK","info":{"list":[{"warehouseCode":"PS0426919682","warehouseName":"Girona Warehouse","saleCountryList":["FR","ES","IT","NL","PL"],"createType":3,"warehouseType":1,"authServiceCode":"","authServiceName":""},{"warehouseCode":"PS1993127180","warehouseName":"EU Warehouse","saleCountryList":["DE","FR","ES","IT","NL","PL"],"createType":3,"warehouseType":1,"authServiceCode":"","authServiceName":""},{"warehouseCode":"PS8428226538","warehouseName":"英国","saleCountryList":["GB"],"createType":1,"warehouseType":1,"authServiceCode":"","authServiceName":""}]},"traceId":"trace_1750831578335"}}
2025-06-25 14:12:47 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"商品一键上架SHEIN","startTime":1750831967639,"spendTime":2,"basePath":"http://localhost:8080","uri":"/shein/product/create","url":"http://localhost:8080/shein/product/create","method":"POST","ip":"************","parameter":{"categoryId":0,"supplierCode":"string","multiLanguageNameList":[{"language":"string","name":"string"}],"multiLanguageDescList":[{"language":"string","name":"string"}],"productAttributeList":[{"attributeId":0,"attributeValueId":0,"attributeExtraValue":"string"}],"skcList":[{"supplierCode":"string","saleAttribute":{"attributeId":0,"attributeValueId":0},"skuList":[{"supplierSku":"string","basePrice":0,"currency":"string","inventoryNum":0,"supplierWarehouseId":"string","specialPrice":0,"mallState":"string","length":"string","width":"string","height":"string","weight":"string","stockInfoList":[{"inventoryNum":0,"supplierWarehouseId":"string"}]}]}],"siteList":[{"mainSite":"string","subSiteList":["string"]}],"sourceSystem":"string"},"result":{"success":true,"message":"商品创建成功","sheinProductId":"SHEIN_PRODUCT_1750831967639"}}
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-25 14:14:02.342(Timestamp), ************(String), null, null
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-25 14:14:02 [http-nio-8080-exec-1] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-25 14:14:03 [http-nio-8080-exec-1] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-25 14:14:03 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750832042266,"spendTime":1225,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwODMyMDQyMzM5LCJleHAiOjE3NTE0MzY4NDJ9.IM9le3n7RQnSUAHUPokp4fja0qpywGOyYbeQHE2nllo_NyXnxSUVjzuzpvZlGRsyiEF3upQ-EgIfnMPW5vqHdg"}}}
2025-06-25 14:14:37 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取商品发布规范","startTime":1750832077881,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/publish-spec","url":"http://localhost:8080/shein/product/publish-spec","method":"POST","ip":"************","parameter":{"categoryId":1003,"spuName":"Summer Dress"},"result":{"code":"0","msg":"success","info":{"fillInStandardList":[{"module":"basic_info","fieldKey":"product_name","required":true,"show":true},{"module":"basic_info","fieldKey":"product_description","required":true,"show":true}],"defaultLanguage":"en","pictureConfigList":[{"pictureType":"main_image","minCount":1,"maxCount":10,"requirement":"主图要求：尺寸不小于800x800像素"}],"currency":"USD","supportSaleAttributeSort":true},"bbl":"test_bbl","traceId":"trace_1750832077881"}}
2025-06-25 14:14:47 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取可用品牌列表","startTime":1750832087179,"spendTime":1,"basePath":"http://localhost:8080","uri":"/shein/product/brands","url":"http://localhost:8080/shein/product/brands","method":"POST","ip":"************","result":{"code":"0","msg":"success","info":{"data":[{"brandCode":"SHEIN","brandName":"SHEIN"},{"brandCode":"ROMWE","brandName":"ROMWE"},{"brandCode":"DAZY","brandName":"DAZY"}],"meta":{"count":3}},"bbl":"test_bbl"}}
2025-06-25 14:14:54 [http-nio-8080-exec-4] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"获取类目树","startTime":1750832094853,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/categories","url":"http://localhost:8080/shein/product/categories","method":"POST","ip":"************","result":{"code":"0","msg":"success","info":{"data":[{"categoryId":1001,"productTypeId":2001,"parentCategoryId":0,"categoryName":"服装","lastCategory":false,"children":[{"categoryId":1002,"productTypeId":2002,"parentCategoryId":1001,"categoryName":"女装","lastCategory":false,"children":[{"categoryId":1003,"productTypeId":2003,"parentCategoryId":1002,"categoryName":"连衣裙","lastCategory":true},{"categoryId":1004,"productTypeId":2004,"parentCategoryId":1002,"categoryName":"上衣","lastCategory":true}]},{"categoryId":1005,"productTypeId":2005,"parentCategoryId":1001,"categoryName":"男装","lastCategory":false,"children":[{"categoryId":1006,"productTypeId":2006,"parentCategoryId":1005,"categoryName":"衬衫","lastCategory":true}]}]},{"categoryId":2001,"productTypeId":3001,"parentCategoryId":0,"categoryName":"配饰","lastCategory":false,"children":[{"categoryId":2002,"productTypeId":3002,"parentCategoryId":2001,"categoryName":"包包","lastCategory":true}]}],"meta":{"count":2}},"bbl":"test_bbl"}}
2025-06-25 14:15:17 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可选属性","startTime":1750832117014,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/attribute-template","url":"http://localhost:8080/shein/product/attribute-template","method":"POST","ip":"************","parameter":{"productTypeIdList":[2023,2024]},"result":{"code":"0","msg":"success","info":{"data":[{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":2023},{"attributeInfos":[{"attributeId":1001,"attributeName":"颜色","attributeIsShow":1,"attributeType":1,"attributeLabel":1,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1,2],"attributeValueInfoList":[{"attributeValue":"红色","attributeValueId":10001,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"蓝色","attributeValueId":10002,"isShow":1,"isCustomAttributeValue":false}]},{"attributeId":1002,"attributeName":"尺寸","attributeIsShow":1,"attributeType":1,"attributeLabel":2,"attributeMode":1,"attributeInputNum":1,"attributeStatus":1,"attributeRemarkList":[1],"attributeValueInfoList":[{"attributeValue":"S","attributeValueId":10003,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"M","attributeValueId":10004,"isShow":1,"isCustomAttributeValue":false},{"attributeValue":"L","attributeValueId":10005,"isShow":1,"isCustomAttributeValue":false}]}],"supplierId":12345,"productTypeId":2024}],"meta":{"count":2}},"bbl":"test_bbl","traceId":"trace_1750832117014"}}
2025-06-25 14:15:34 [http-nio-8080-exec-5] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询是否支持自定义属性值","startTime":1750832134349,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/custom-attribute-permission","url":"http://localhost:8080/shein/product/custom-attribute-permission","method":"POST","ip":"************","parameter":{"categoryIdList":[1003,1004]},"result":{"code":"0","msg":"success","info":{"data":[{"hasPermission":1,"lastCategoryId":1003,"attributeId":1001},{"hasPermission":0,"lastCategoryId":1003,"attributeId":1002},{"hasPermission":1,"lastCategoryId":1004,"attributeId":1001},{"hasPermission":0,"lastCategoryId":1004,"attributeId":1002}],"meta":{"count":4}},"bbl":"test_bbl","traceId":"trace_1750832134349"}}
2025-06-25 14:15:54 [http-nio-8080-exec-8] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加自定义属性值","startTime":1750832154211,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/add-custom-attribute-value","url":"http://localhost:8080/shein/product/add-custom-attribute-value","method":"POST","ip":"************","parameter":{"attributeId":1001,"attributeValue":"自定义颜色","categoryId":1003,"attributeValueNameMultis":[{"language":"en","attributeValueNameMulti":"Custom Color"}]},"result":{"code":"0","msg":"success","info":{"supplierId":12345,"supplierSource":1,"categoryId":1003,"attributeId":1001,"attributeValueId":1750832154211,"attributeValueName":"自定义颜色","attributeValueMultiArr":[{"attributeValueNameMulti":"Custom Color","language":"en"}]},"bbl":"test_bbl","traceId":"trace_1750832154211"}}
2025-06-25 14:16:32 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"图片链接转换","startTime":1750832192262,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/transform-pic","url":"http://localhost:8080/shein/product/transform-pic","method":"POST","ip":"************","parameter":{"imageType":1,"originalUrl":"https://example.com/image.jpg"},"result":{"code":"0","msg":"success","info":{"original":"https://example.com/image.jpg","transformed":"https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1750832192262.jpg"},"bbl":"test_bbl","traceId":"trace_1750832192262"}}
2025-06-25 14:16:51 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺可售站点","startTime":1750832211718,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/site-list","url":"http://localhost:8080/shein/product/site-list","method":"POST","ip":"************","parameter":{},"result":{"code":"0","msg":"success","info":{"data":[{"mainSite":"US","mainSiteName":"美国","subSiteList":[{"siteName":"美国","siteAbbr":"US","siteStatus":1,"storeType":1}]},{"mainSite":"EU","mainSiteName":"欧洲","subSiteList":[{"siteName":"德国","siteAbbr":"DE","siteStatus":1,"storeType":1},{"siteName":"法国","siteAbbr":"FR","siteStatus":1,"storeType":1},{"siteName":"意大利","siteAbbr":"IT","siteStatus":1,"storeType":1}]},{"mainSite":"ASIA","mainSiteName":"亚洲","subSiteList":[{"siteName":"日本","siteAbbr":"JP","siteStatus":1,"storeType":1},{"siteName":"韩国","siteAbbr":"KR","siteStatus":1,"storeType":1}]}],"meta":{"count":3}},"bbl":"test_bbl","traceId":"trace_1750832211718"}}
2025-06-25 14:19:06 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商家仓库列表(自主运营和半托管模式)","startTime":1750832346735,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/warehouse-list","url":"http://localhost:8080/shein/product/warehouse-list","method":"GET","ip":"************","result":{"code":"0","msg":"OK","info":{"list":[{"warehouseCode":"PS0426919682","warehouseName":"Girona Warehouse","saleCountryList":["FR","ES","IT","NL","PL"],"createType":3,"warehouseType":1,"authServiceCode":"","authServiceName":""},{"warehouseCode":"PS1993127180","warehouseName":"EU Warehouse","saleCountryList":["DE","FR","ES","IT","NL","PL"],"createType":3,"warehouseType":1,"authServiceCode":"","authServiceName":""},{"warehouseCode":"PS8428226538","warehouseName":"英国","saleCountryList":["GB"],"createType":1,"warehouseType":1,"authServiceCode":"","authServiceName":""}]},"traceId":"trace_1750832346735"}}
2025-06-25 14:25:10 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 14:25:10 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 18:22:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 18:22:25 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 1920 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-25 18:22:25 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 18:22:25 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 18:22:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 18:22:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 18:22:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 87 ms. Found 0 Redis repository interfaces.
2025-06-25 18:22:27 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$24ca6088] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 18:22:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 18:22:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 18:22:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 18:22:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 18:22:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 18:22:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2353 ms
2025-06-25 18:22:27 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 18:22:38 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 18:22:40 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 18:22:40 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 18:22:40 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 18:22:40 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 18:22:40 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 18:22:41 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 18:22:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@32cda33c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bb24266, org.springframework.security.web.context.SecurityContextPersistenceFilter@ea3491d, org.springframework.security.web.header.HeaderWriterFilter@52238861, org.springframework.security.web.authentication.logout.LogoutFilter@622e39d, com.macro.mall.security.component.JwtAuthenticationTokenFilter@32121140, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@60b8c05c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62d72091, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63437af4, org.springframework.security.web.session.SessionManagementFilter@75937998, org.springframework.security.web.access.ExceptionTranslationFilter@70d24586, com.macro.mall.security.component.DynamicSecurityFilter@2148b47e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@541d8a9e]
2025-06-25 18:22:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 18:22:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 18:22:42 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 18:22:43 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 18.294 seconds (JVM running for 18.837)
2025-06-25 18:22:43 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 18:22:43 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 18:22:43 [RMI TCP Connection(3)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 18:23:25 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:23:25 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:23:25 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/platforms]
2025-06-25 18:23:25 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/platforms]
2025-06-25 18:23:25 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询平台列表","username":"testls","startTime":1750847005927,"spendTime":4,"basePath":"http://localhost:8080","uri":"/pendingpublish/platforms","url":"http://localhost:8080/pendingpublish/platforms","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":[{"1":"希音"},{"2":"京东"}]}}
2025-06-25 18:23:51 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:23:51 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:23:51 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops]
2025-06-25 18:23:51 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops]
2025-06-25 18:23:51 [http-nio-8080-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'platformId' for method parameter type Long is not present]
2025-06-25 18:23:51 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /error]
2025-06-25 18:24:36 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:24:36 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:24:36 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops]
2025-06-25 18:24:36 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops]
2025-06-25 18:24:36 [http-nio-8080-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'platformId' for method parameter type Long is not present]
2025-06-25 18:24:36 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /error]
2025-06-25 18:25:03 [http-nio-8080-exec-7] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:25:03 [http-nio-8080-exec-7] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:25:03 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops?platformId=1]
2025-06-25 18:25:03 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops?platformId=1]
2025-06-25 18:25:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.S.selectSupplierIdByUserId - ==>  Preparing: SELECT supplier_id FROM shein_auth WHERE user_id = ? AND valid = 1
2025-06-25 18:25:03 [http-nio-8080-exec-7] DEBUG c.m.m.m.S.selectSupplierIdByUserId - ==> Parameters: 20(Long)
2025-06-25 18:25:04 [http-nio-8080-exec-7] DEBUG c.m.m.m.S.selectSupplierIdByUserId - <==      Total: 0
2025-06-25 18:25:04 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺列表","username":"testls","startTime":1750847103640,"spendTime":474,"basePath":"http://localhost:8080","uri":"/pendingpublish/shops","url":"http://localhost:8080/pendingpublish/shops","method":"GET","ip":"*************","parameter":{"platformId":1},"result":{"code":200,"message":"操作成功","data":[]}}
2025-06-25 18:29:42 [http-nio-8080-exec-10] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:29:42 [http-nio-8080-exec-10] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:29:42 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops?platformId=1]
2025-06-25 18:29:42 [http-nio-8080-exec-10] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/shops?platformId=1]
2025-06-25 18:29:42 [http-nio-8080-exec-10] DEBUG c.m.m.m.S.selectSupplierIdByUserId - ==>  Preparing: SELECT supplier_id FROM shein_auth WHERE user_id = ? AND valid = 1
2025-06-25 18:29:42 [http-nio-8080-exec-10] DEBUG c.m.m.m.S.selectSupplierIdByUserId - ==> Parameters: 20(Long)
2025-06-25 18:29:42 [http-nio-8080-exec-10] DEBUG c.m.m.m.S.selectSupplierIdByUserId - <==      Total: 1
2025-06-25 18:29:42 [http-nio-8080-exec-10] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询店铺列表","username":"testls","startTime":1750847382476,"spendTime":463,"basePath":"http://localhost:8080","uri":"/pendingpublish/shops","url":"http://localhost:8080/pendingpublish/shops","method":"GET","ip":"*************","parameter":{"platformId":1},"result":{"code":200,"message":"操作成功","data":[111]}}
2025-06-25 18:33:04 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:33:04 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:33:04 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:33:04 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:33:04 [http-nio-8080-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.macro.mall.common.api.CommonResult com.macro.mall.controller.PendingPublishController.addShoppingCart(com.macro.mall.dto.PendingPublishDTO)]
2025-06-25 18:33:04 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:33:33 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:33:33 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:33:33 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:33:33 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:33:33 [http-nio-8080-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.macro.mall.common.api.CommonResult com.macro.mall.controller.PendingPublishController.addShoppingCart(com.macro.mall.dto.PendingPublishDTO)]
2025-06-25 18:33:33 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error?platformId=1&productId=2077&supplierId=111&number=1]
2025-06-25 18:36:26 [http-nio-8080-exec-7] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:36:26 [http-nio-8080-exec-7] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:36:26 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:36:26 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:36:27 [http-nio-8080-exec-7] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:36:27 [http-nio-8080-exec-7] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: null
2025-06-25 18:36:27 [http-nio-8080-exec-7] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 0
2025-06-25 18:36:27 [http-nio-8080-exec-7] ERROR c.m.m.s.i.PendingPublishServiceImpl - 商品不存在，商品ID: null
2025-06-25 18:36:27 [http-nio-8080-exec-7] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.RuntimeException: 商品不存在] with root cause
java.lang.RuntimeException: 商品不存在
	at com.macro.mall.service.impl.PendingPublishServiceImpl.addPendingPublish(PendingPublishServiceImpl.java:89)
	at com.macro.mall.controller.PendingPublishController.addShoppingCart(PendingPublishController.java:44)
	at com.macro.mall.controller.PendingPublishController$$FastClassBySpringCGLIB$$6cce3c7b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.PendingPublishController$$EnhancerBySpringCGLIB$$b2a9f34b.addShoppingCart(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 18:36:27 [http-nio-8080-exec-7] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 18:39:41 [http-nio-8080-exec-9] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:39:41 [http-nio-8080-exec-9] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: null
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 0
2025-06-25 18:39:41 [http-nio-8080-exec-9] ERROR c.m.m.s.i.PendingPublishServiceImpl - 商品不存在，商品ID: null
2025-06-25 18:39:41 [http-nio-8080-exec-9] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.RuntimeException: 商品不存在] with root cause
java.lang.RuntimeException: 商品不存在
	at com.macro.mall.service.impl.PendingPublishServiceImpl.addPendingPublish(PendingPublishServiceImpl.java:89)
	at com.macro.mall.controller.PendingPublishController.addShoppingCart(PendingPublishController.java:44)
	at com.macro.mall.controller.PendingPublishController$$FastClassBySpringCGLIB$$6cce3c7b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.PendingPublishController$$EnhancerBySpringCGLIB$$b2a9f34b.addShoppingCart(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 18:39:41 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 18:40:49 [http-nio-8080-exec-1] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:40:49 [http-nio-8080-exec-1] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:40:49 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/list]
2025-06-25 18:40:49 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/list]
2025-06-25 18:40:49 [http-nio-8080-exec-1] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==>  Preparing: SELECT * FROM pms_pending_publish WHERE user_id = ?
2025-06-25 18:40:49 [http-nio-8080-exec-1] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==> Parameters: 20(Long)
2025-06-25 18:40:49 [http-nio-8080-exec-1] DEBUG c.m.m.m.P.selectByUserIdAndProductId - <==      Total: 0
2025-06-25 18:40:49 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"username":"testls","startTime":1750848049163,"spendTime":461,"basePath":"http://localhost:8080","uri":"/pendingpublish/list","url":"http://localhost:8080/pendingpublish/list","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":[]}}
2025-06-25 18:42:54 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:42:54 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: null
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 0
2025-06-25 18:42:54 [http-nio-8080-exec-3] ERROR c.m.m.s.i.PendingPublishServiceImpl - 商品不存在，商品ID: null
2025-06-25 18:42:54 [http-nio-8080-exec-3] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.RuntimeException: 商品不存在] with root cause
java.lang.RuntimeException: 商品不存在
	at com.macro.mall.service.impl.PendingPublishServiceImpl.addPendingPublish(PendingPublishServiceImpl.java:89)
	at com.macro.mall.controller.PendingPublishController.addShoppingCart(PendingPublishController.java:44)
	at com.macro.mall.controller.PendingPublishController$$FastClassBySpringCGLIB$$6cce3c7b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.macro.mall.common.log.WebLogAspect.doAround(WebLogAspect.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.macro.mall.controller.PendingPublishController$$EnhancerBySpringCGLIB$$b2a9f34b.addShoppingCart(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.DynamicSecurityFilter.doFilter(DynamicSecurityFilter.java:57)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.macro.mall.security.component.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-25 18:42:54 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /error]
2025-06-25 18:45:48 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 18:45:48 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 18:45:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 18:45:52 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 5356 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-25 18:45:52 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 18:45:52 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 18:45:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 18:45:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 18:45:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 71 ms. Found 0 Redis repository interfaces.
2025-06-25 18:45:53 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$5be40c78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 18:45:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 18:45:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 18:45:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 18:45:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 18:45:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 18:45:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1905 ms
2025-06-25 18:45:54 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 18:46:05 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 18:46:06 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 18:46:06 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 18:46:06 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 18:46:06 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 18:46:06 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 18:46:07 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 18:46:07 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@39ff4421, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a59780b, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d7d7b1a, org.springframework.security.web.header.HeaderWriterFilter@45f32b12, org.springframework.security.web.authentication.logout.LogoutFilter@7af5ce66, com.macro.mall.security.component.JwtAuthenticationTokenFilter@6d4f266, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65011123, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@20e4e27f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5205b3e2, org.springframework.security.web.session.SessionManagementFilter@395854dd, org.springframework.security.web.access.ExceptionTranslationFilter@43a9988a, com.macro.mall.security.component.DynamicSecurityFilter@47224d5d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@9742012]
2025-06-25 18:46:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 18:46:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 18:46:08 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 18:46:09 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 17.085 seconds (JVM running for 17.556)
2025-06-25 18:46:09 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 18:46:09 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 18:46:09 [RMI TCP Connection(2)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-25 18:46:22 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:46:22 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:46:22 [http-nio-8080-exec-2] INFO  c.m.m.s.i.PendingPublishServiceImpl - 添加购物车，用户ID: 20, 商品ID: 2075, 商品名称: null, 平台ID: 1, 店铺ID: 111, 上架数量: 5
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: 2075(Long)
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 1
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==>  Preparing: SELECT * FROM pms_pending_publish WHERE user_id = ? AND product_id = ? AND supplier_id = ? AND platform_id = ? AND number = ?
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==> Parameters: 20(Long), 2075(Long), 111(Long), 1(Long), 5(Integer)
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.P.selectByUserIdAndProductId - <==      Total: 0
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.PendingPublishMapper.insert - ==>  Preparing: INSERT INTO pms_pending_publish (user_id, product_id, supplier_id, platform_id, name, status, number, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-25 18:46:22 [http-nio-8080-exec-2] DEBUG c.m.m.m.PendingPublishMapper.insert - ==> Parameters: 20(Long), 2075(Long), 111(Long), 1(Long), 一次性竹刀 (String), 0(Integer), 5(Integer), 2025-06-25T18:46:22.821802300(LocalDateTime), 2025-06-25T18:46:22.821802300(LocalDateTime)
2025-06-25 18:46:23 [http-nio-8080-exec-2] DEBUG c.m.m.m.PendingPublishMapper.insert - <==    Updates: 1
2025-06-25 18:46:23 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加购物车","username":"testls","startTime":1750848382325,"spendTime":985,"basePath":"http://localhost:8080","uri":"/pendingpublish/add","url":"http://localhost:8080/pendingpublish/add","method":"POST","ip":"*************","parameter":{"productId":2075,"supplierId":111,"platformId":1,"number":5},"result":{"code":200,"message":"操作成功"}}
2025-06-25 18:46:51 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:46:51 [http-nio-8080-exec-3] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:46:51 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/list]
2025-06-25 18:46:51 [http-nio-8080-exec-3] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/list]
2025-06-25 18:46:51 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==>  Preparing: SELECT * FROM pms_pending_publish WHERE user_id = ?
2025-06-25 18:46:51 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==> Parameters: 20(Long)
2025-06-25 18:46:52 [http-nio-8080-exec-3] DEBUG c.m.m.m.P.selectByUserIdAndProductId - <==      Total: 1
2025-06-25 18:46:52 [http-nio-8080-exec-3] INFO  c.macro.mall.common.log.WebLogAspect - {"username":"testls","startTime":1750848411763,"spendTime":246,"basePath":"http://localhost:8080","uri":"/pendingpublish/list","url":"http://localhost:8080/pendingpublish/list","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":[{"id":1,"name":"一次性竹刀 ","status":0,"number":5}]}}
2025-06-25 18:47:35 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:47:35 [http-nio-8080-exec-4] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:47:35 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/clean]
2025-06-25 18:47:35 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /pendingpublish/clean]
2025-06-25 18:47:35 [http-nio-8080-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-25 18:47:35 [http-nio-8080-exec-4] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /error]
2025-06-25 18:47:44 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:47:44 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:47:44 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [DELETE /pendingpublish/clean]
2025-06-25 18:47:44 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [DELETE /pendingpublish/clean]
2025-06-25 18:47:44 [http-nio-8080-exec-5] DEBUG c.m.m.m.P.deleteByUserId - ==>  Preparing: delete from pms_pending_publish where user_id = ?
2025-06-25 18:47:44 [http-nio-8080-exec-5] DEBUG c.m.m.m.P.deleteByUserId - ==> Parameters: 20(Long)
2025-06-25 18:47:45 [http-nio-8080-exec-5] DEBUG c.m.m.m.P.deleteByUserId - <==    Updates: 1
2025-06-25 18:47:45 [http-nio-8080-exec-5] INFO  c.macro.mall.common.log.WebLogAspect - {"username":"testls","startTime":1750848464801,"spendTime":499,"basePath":"http://localhost:8080","uri":"/pendingpublish/clean","url":"http://localhost:8080/pendingpublish/clean","method":"DELETE","ip":"*************","result":{"code":200,"message":"操作成功"}}
2025-06-25 18:48:11 [http-nio-8080-exec-6] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:48:11 [http-nio-8080-exec-6] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:48:11 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:48:11 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:48:11 [http-nio-8080-exec-6] INFO  c.m.m.s.i.PendingPublishServiceImpl - 添加购物车，用户ID: 20, 商品ID: 2075, 商品名称: null, 平台ID: 1, 店铺ID: 111, 上架数量: 5
2025-06-25 18:48:11 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:48:11 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: 2075(Long)
2025-06-25 18:48:12 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 1
2025-06-25 18:48:12 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==>  Preparing: SELECT * FROM pms_pending_publish WHERE user_id = ? AND product_id = ? AND supplier_id = ? AND platform_id = ? AND number = ?
2025-06-25 18:48:12 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==> Parameters: 20(Long), 2075(Long), 111(Long), 1(Long), 5(Integer)
2025-06-25 18:48:14 [http-nio-8080-exec-6] DEBUG c.m.m.m.P.selectByUserIdAndProductId - <==      Total: 0
2025-06-25 18:48:14 [http-nio-8080-exec-6] DEBUG c.m.m.m.PendingPublishMapper.insert - ==>  Preparing: INSERT INTO pms_pending_publish (user_id, product_id, supplier_id, platform_id, name, status, number, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-25 18:48:14 [http-nio-8080-exec-6] DEBUG c.m.m.m.PendingPublishMapper.insert - ==> Parameters: 20(Long), 2075(Long), 111(Long), 1(Long), 一次性竹刀 (String), 0(Integer), 5(Integer), 2025-06-25T18:48:14.759440800(LocalDateTime), 2025-06-25T18:48:14.759440800(LocalDateTime)
2025-06-25 18:48:15 [http-nio-8080-exec-6] DEBUG c.m.m.m.PendingPublishMapper.insert - <==    Updates: 1
2025-06-25 18:48:15 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加购物车","username":"testls","startTime":1750848491036,"spendTime":4221,"basePath":"http://localhost:8080","uri":"/pendingpublish/add","url":"http://localhost:8080/pendingpublish/add","method":"POST","ip":"*************","parameter":{"productId":2075,"supplierId":111,"platformId":1,"number":5},"result":{"code":200,"message":"操作成功"}}
2025-06-25 18:52:09 [http-nio-8080-exec-9] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 18:52:09 [http-nio-8080-exec-9] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 18:52:09 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:52:09 [http-nio-8080-exec-9] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [POST /pendingpublish/add]
2025-06-25 18:52:09 [http-nio-8080-exec-9] INFO  c.m.m.s.i.PendingPublishServiceImpl - 添加购物车，用户ID: 20, 商品ID: 2077, 商品名称: null, 平台ID: 1, 店铺ID: 111, 上架数量: 5
2025-06-25 18:52:11 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - ==>  Preparing: SELECT * FROM pms_product WHERE id = ?
2025-06-25 18:52:11 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - ==> Parameters: 2077(Long)
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByProductId - <==      Total: 1
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==>  Preparing: SELECT * FROM pms_pending_publish WHERE user_id = ? AND product_id = ? AND supplier_id = ? AND platform_id = ? AND number = ?
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByUserIdAndProductId - ==> Parameters: 20(Long), 2077(Long), 111(Long), 1(Long), 5(Integer)
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.P.selectByUserIdAndProductId - <==      Total: 0
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.PendingPublishMapper.insert - ==>  Preparing: INSERT INTO pms_pending_publish (user_id, product_id, supplier_id, platform_id, name, status, number, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-25 18:52:12 [http-nio-8080-exec-9] DEBUG c.m.m.m.PendingPublishMapper.insert - ==> Parameters: 20(Long), 2077(Long), 111(Long), 1(Long), 雪糕棒(String), 0(Integer), 5(Integer), 2025-06-25T18:52:12.716760(LocalDateTime), 2025-06-25T18:52:12.716760(LocalDateTime)
2025-06-25 18:52:13 [http-nio-8080-exec-9] DEBUG c.m.m.m.PendingPublishMapper.insert - <==    Updates: 1
2025-06-25 18:52:13 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"添加购物车","username":"testls","startTime":1750848729791,"spendTime":3423,"basePath":"http://localhost:8080","uri":"/pendingpublish/add","url":"http://localhost:8080/pendingpublish/add","method":"POST","ip":"*************","parameter":{"productId":2077,"supplierId":111,"platformId":1,"number":5},"result":{"code":200,"message":"操作成功"}}
2025-06-25 19:00:41 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 19:00:41 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==>  Preparing: SELECT *, pc.parent_id cateParentId, l.id ladder_id,l.product_id ladder_product_id,l.discount ladder_discount,l.count ladder_count,l.price ladder_price, pf.id full_id,pf.product_id full_product_id,pf.full_price full_full_price,pf.reduce_price full_reduce_price, m.id member_id,m.product_id member_product_id,m.member_level_id member_member_level_id,m.member_price member_member_price,m.member_level_name member_member_level_name, s.id sku_id,s.product_id sku_product_id,s.price sku_price,s.promotion_price sku_promotion_price,s.low_stock sku_low_stock,s.pic sku_pic,s.sale sku_sale,s.sku_code sku_sku_code,s.stock sku_stock,s.sp_data sku_sp_data, a.id attribute_id,a.product_id attribute_product_id,a.product_attribute_id attribute_product_attribute_id,a.value attribute_value FROM pms_product p LEFT JOIN pms_product_category pc on pc.id = p.product_category_id LEFT JOIN pms_product_ladder l ON p.id = l.product_id LEFT JOIN pms_product_full_reduction pf ON pf.product_id=p.id LEFT JOIN pms_member_price m ON m.product_id = p.id LEFT JOIN pms_sku_stock s ON s.product_id = p.id LEFT JOIN pms_product_attribute_value a ON a.product_id=p.id WHERE p.id=?;
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==> Parameters: 2077(Long)
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====>  Preparing: select * from cms_subject_product_relation where product_id=?
2025-06-25 19:00:41 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:00:42 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - <====      Total: 0
2025-06-25 19:00:42 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====>  Preparing: select * from cms_prefrence_area_product_relation where product_id=?
2025-06-25 19:00:42 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:00:42 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - <====      Total: 0
2025-06-25 19:00:42 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - <==      Total: 1
2025-06-25 19:00:42 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"根据商品id获取商品编辑信息","username":"testls","startTime":1750849241084,"spendTime":1239,"basePath":"http://localhost:8080","uri":"/product/updateInfo/2077","url":"http://localhost:8080/product/updateInfo/2077","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":{"cateParentId":1000,"productLadderList":[],"productFullReductionList":[],"memberPriceList":[],"skuStockList":[],"productAttributeValueList":[],"subjectProductRelationList":[],"prefrenceAreaProductRelationList":[],"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":1011,"productCategoryName":"厨房餐饮","productCateUplevelId":1000,"productCateUplevelName":"家居日用","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}}}
2025-06-25 19:29:58 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 19:29:58 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 19:30:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 19:30:04 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 28228 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-25 19:30:04 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 19:30:04 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 19:30:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 19:30:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 19:30:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 81 ms. Found 0 Redis repository interfaces.
2025-06-25 19:30:06 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$5be40c78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 19:30:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 19:30:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 19:30:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 19:30:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 19:30:06 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 19:30:06 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2014 ms
2025-06-25 19:30:06 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 19:30:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 19:30:25 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 19:30:25 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 19:30:26 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 19:30:26 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 19:30:26 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 19:30:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 19:30:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5ab7e997, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2552cb80, org.springframework.security.web.context.SecurityContextPersistenceFilter@715ea64a, org.springframework.security.web.header.HeaderWriterFilter@2dc54b50, org.springframework.security.web.authentication.logout.LogoutFilter@46c7e6c0, com.macro.mall.security.component.JwtAuthenticationTokenFilter@6d4f266, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b4bc7b9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@12d33d7a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b3b5252, org.springframework.security.web.session.SessionManagementFilter@65011123, org.springframework.security.web.access.ExceptionTranslationFilter@456beb8b, com.macro.mall.security.component.DynamicSecurityFilter@47224d5d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2802caa2]
2025-06-25 19:30:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 19:30:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 19:30:28 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 19:30:28 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 24.915 seconds (JVM running for 25.433)
2025-06-25 19:30:29 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 19:30:29 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 19:30:29 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 19:30:51 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 19:30:51 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 19:30:51 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:30:51 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:30:51 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==>  Preparing: SELECT *, pc.parent_id cateParentId, l.id ladder_id,l.product_id ladder_product_id,l.discount ladder_discount,l.count ladder_count,l.price ladder_price, pf.id full_id,pf.product_id full_product_id,pf.full_price full_full_price,pf.reduce_price full_reduce_price, m.id member_id,m.product_id member_product_id,m.member_level_id member_member_level_id,m.member_price member_member_price,m.member_level_name member_member_level_name, s.id sku_id,s.product_id sku_product_id,s.price sku_price,s.promotion_price sku_promotion_price,s.low_stock sku_low_stock,s.pic sku_pic,s.sale sku_sale,s.sku_code sku_sku_code,s.stock sku_stock,s.sp_data sku_sp_data, a.id attribute_id,a.product_id attribute_product_id,a.product_attribute_id attribute_product_attribute_id,a.value attribute_value FROM pms_product p LEFT JOIN pms_product_category pc on pc.id = p.product_category_id LEFT JOIN pms_product_ladder l ON p.id = l.product_id LEFT JOIN pms_product_full_reduction pf ON pf.product_id=p.id LEFT JOIN pms_member_price m ON m.product_id = p.id LEFT JOIN pms_sku_stock s ON s.product_id = p.id LEFT JOIN pms_product_attribute_value a ON a.product_id=p.id WHERE p.id=?;
2025-06-25 19:30:51 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==> Parameters: 2077(Long)
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====>  Preparing: select * from cms_subject_product_relation where product_id=?
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - <====      Total: 0
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====>  Preparing: select * from cms_prefrence_area_product_relation where product_id=?
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - <====      Total: 0
2025-06-25 19:30:53 [http-nio-8080-exec-2] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - <==      Total: 1
2025-06-25 19:30:53 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"根据商品id获取商品编辑信息","username":"testls","startTime":1750851051693,"spendTime":1947,"basePath":"http://localhost:8080","uri":"/product/updateInfo/2077","url":"http://localhost:8080/product/updateInfo/2077","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":{"cateParentId":1000,"productLadderList":[],"productFullReductionList":[],"memberPriceList":[],"skuStockList":[],"productAttributeValueList":[],"subjectProductRelationList":[],"prefrenceAreaProductRelationList":[],"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":1011,"productCategoryName":"厨房餐饮","productCateUplevelId":1000,"productCateUplevelName":"家居日用","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":999,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}}}
2025-06-25 19:37:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 19:37:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 19:37:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 19:37:49 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on DESKTOP-3ARLQBI with PID 5416 (D:\ideaproject\Mall\mall-admin\target\classes started by Administrator in D:\ideaproject\Mall)
2025-06-25 19:37:49 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-25 19:37:49 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-25 19:37:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 19:37:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 19:37:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 93 ms. Found 0 Redis repository interfaces.
2025-06-25 19:37:50 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$5be40c78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-25 19:37:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-25 19:37:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 19:37:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 19:37:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 19:37:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 19:37:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2043 ms
2025-06-25 19:37:51 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 19:38:02 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 19:38:04 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-25 19:38:04 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-25 19:38:04 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-25 19:38:04 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-25 19:38:04 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-25 19:38:05 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-25 19:38:05 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@118d7e0e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@39ff4421, org.springframework.security.web.context.SecurityContextPersistenceFilter@2f9d47a3, org.springframework.security.web.header.HeaderWriterFilter@611ffa8d, org.springframework.security.web.authentication.logout.LogoutFilter@68fec965, com.macro.mall.security.component.JwtAuthenticationTokenFilter@29bf90fc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@67836d4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65011123, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7a59780b, org.springframework.security.web.session.SessionManagementFilter@16a89351, org.springframework.security.web.access.ExceptionTranslationFilter@622e39d, com.macro.mall.security.component.DynamicSecurityFilter@537a8915, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7fd7965b]
2025-06-25 19:38:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 19:38:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 19:38:06 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-25 19:38:07 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 18.387 seconds (JVM running for 18.943)
2025-06-25 19:38:07 [RMI TCP Connection(5)-*************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 19:38:07 [RMI TCP Connection(5)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 19:38:07 [RMI TCP Connection(5)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 19:39:49 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 19:39:49 [http-nio-8080-exec-2] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 19:39:49 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/update/2077] with attributes [5:商品管理]
2025-06-25 19:39:49 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/update/2077] with attributes [5:商品管理]
2025-06-25 19:39:49 [http-nio-8080-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-25 19:39:49 [http-nio-8080-exec-2] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /error]
2025-06-25 19:40:26 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 19:40:26 [http-nio-8080-exec-5] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 19:40:26 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [POST /product/update/2077] with attributes [5:商品管理]
2025-06-25 19:40:26 [http-nio-8080-exec-5] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [POST /product/update/2077] with attributes [5:商品管理]
2025-06-25 19:40:26 [http-nio-8080-exec-5] DEBUG c.m.m.m.PmsProductMapper.updateById - ==>  Preparing: UPDATE pms_product SET stock = ? WHERE id = ?
2025-06-25 19:40:26 [http-nio-8080-exec-5] DEBUG c.m.m.m.PmsProductMapper.updateById - ==> Parameters: 998(Integer), 2077(Long)
2025-06-25 19:40:27 [http-nio-8080-exec-5] DEBUG c.m.m.m.PmsProductMapper.updateById - <==    Updates: 1
2025-06-25 19:40:27 [http-nio-8080-exec-5] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"更新商品","username":"testls","startTime":1750851626391,"spendTime":729,"basePath":"http://localhost:8080","uri":"/product/update/2077","url":"http://localhost:8080/product/update/2077","method":"POST","ip":"*************","parameter":{"id":2077,"stock":998},"result":{"code":200,"message":"操作成功","data":1}}
2025-06-25 19:41:21 [http-nio-8080-exec-6] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - checking username:testls
2025-06-25 19:41:21 [http-nio-8080-exec-6] INFO  c.m.m.s.c.JwtAuthenticationTokenFilter - authenticated user:testls
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized filter invocation [GET /product/updateInfo/2077] with attributes [5:商品管理]
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==>  Preparing: SELECT *, pc.parent_id cateParentId, l.id ladder_id,l.product_id ladder_product_id,l.discount ladder_discount,l.count ladder_count,l.price ladder_price, pf.id full_id,pf.product_id full_product_id,pf.full_price full_full_price,pf.reduce_price full_reduce_price, m.id member_id,m.product_id member_product_id,m.member_level_id member_member_level_id,m.member_price member_member_price,m.member_level_name member_member_level_name, s.id sku_id,s.product_id sku_product_id,s.price sku_price,s.promotion_price sku_promotion_price,s.low_stock sku_low_stock,s.pic sku_pic,s.sale sku_sale,s.sku_code sku_sku_code,s.stock sku_stock,s.sp_data sku_sp_data, a.id attribute_id,a.product_id attribute_product_id,a.product_attribute_id attribute_product_attribute_id,a.value attribute_value FROM pms_product p LEFT JOIN pms_product_category pc on pc.id = p.product_category_id LEFT JOIN pms_product_ladder l ON p.id = l.product_id LEFT JOIN pms_product_full_reduction pf ON pf.product_id=p.id LEFT JOIN pms_member_price m ON m.product_id = p.id LEFT JOIN pms_sku_stock s ON s.product_id = p.id LEFT JOIN pms_product_attribute_value a ON a.product_id=p.id WHERE p.id=?;
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - ==> Parameters: 2077(Long)
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====>  Preparing: select * from cms_subject_product_relation where product_id=?
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectSubjectProductRelationByProductId - <====      Total: 0
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====>  Preparing: select * from cms_prefrence_area_product_relation where product_id=?
2025-06-25 19:41:21 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - ====> Parameters: 2077(Long)
2025-06-25 19:41:22 [http-nio-8080-exec-6] DEBUG c.m.m.d.P.selectPrefrenceAreaProductRelationByProductId - <====      Total: 0
2025-06-25 19:41:22 [http-nio-8080-exec-6] DEBUG c.m.m.d.PmsProductDao.getUpdateInfo - <==      Total: 1
2025-06-25 19:41:22 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"根据商品id获取商品编辑信息","username":"testls","startTime":1750851681209,"spendTime":950,"basePath":"http://localhost:8080","uri":"/product/updateInfo/2077","url":"http://localhost:8080/product/updateInfo/2077","method":"GET","ip":"*************","result":{"code":200,"message":"操作成功","data":{"cateParentId":1000,"productLadderList":[],"productFullReductionList":[],"memberPriceList":[],"skuStockList":[],"productAttributeValueList":[],"subjectProductRelationList":[],"prefrenceAreaProductRelationList":[],"id":2077,"productSn":"1000087877","name":"雪糕棒","pic":"https://img-eu-4.freex.es/img/2320/1000087877/600x600/68f8be5cc5015e4c4a44545964806fd6","subTitle":"PATYCZKI DO LOD覹","productCategoryId":1011,"productCategoryName":"厨房餐饮","productCateUplevelId":1000,"productCateUplevelName":"家居日用","warehouseId":1,"location":"Poland","price":2.05,"originalPrice":2.05,"stock":998,"productCode":"2419788.0","deleteStatus":0,"newStatus":1,"recommandStatus":0,"giftPoint":0,"giftGrowth":0,"shelfTime":1747386952000,"currency":"zł"}}}
2025-06-25 19:57:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-25 19:57:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
