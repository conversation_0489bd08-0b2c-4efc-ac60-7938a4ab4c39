2025-06-27 11:21:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-27 11:21:40 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 70896 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-27 11:21:40 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-27 11:21:40 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-27 11:21:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-27 11:21:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-27 11:21:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92 ms. Found 0 Redis repository interfaces.
2025-06-27 11:21:47 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$83402ec4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-27 11:21:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-27 11:21:47 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-27 11:21:47 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-27 11:21:47 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-27 11:21:47 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-27 11:21:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 11:21:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-27 11:21:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-27 11:21:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-27 11:21:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7064 ms
2025-06-27 11:21:47 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-27 11:22:03 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-27 11:22:06 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-27 11:22:06 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-27 11:22:06 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-27 11:22:06 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-27 11:22:06 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-27 11:22:07 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-27 11:22:08 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f378d7e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@dc28d74, org.springframework.security.web.context.SecurityContextPersistenceFilter@60515ab1, org.springframework.security.web.header.HeaderWriterFilter@69e58566, org.springframework.security.web.authentication.logout.LogoutFilter@5d4af473, com.macro.mall.security.component.JwtAuthenticationTokenFilter@5216532a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16a89351, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@395854dd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@12922d53, org.springframework.security.web.session.SessionManagementFilter@31a9eaa, org.springframework.security.web.access.ExceptionTranslationFilter@517fb2c3, com.macro.mall.security.component.DynamicSecurityFilter@6a95c2f6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5c7876e1]
2025-06-27 11:22:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-27 11:22:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-27 11:22:09 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-27 11:22:09 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 29.587 seconds (JVM running for 30.427)
2025-06-27 11:22:09 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-27 11:22:09 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-27 11:22:09 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-27 11:22:10 [boundedElastic-1] WARN  o.s.b.a.r.RedisReactiveHealthIndicator - Redis health check failed
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:1117)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:509)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:103)
	at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:86)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:227)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 14 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-06-27 11:22:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-27 11:22:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-27 11:22:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-27 11:22:34 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 105764 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-27 11:22:34 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-27 11:22:34 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-27 11:22:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-27 11:22:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-27 11:22:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 90 ms. Found 0 Redis repository interfaces.
2025-06-27 11:22:36 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$b15115c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-27 11:22:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-27 11:22:36 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-27 11:22:36 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-27 11:22:36 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-27 11:22:36 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-27 11:22:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 11:22:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-27 11:22:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-27 11:22:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-27 11:22:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2202 ms
2025-06-27 11:22:36 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-27 11:22:52 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-27 11:22:54 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-27 11:22:54 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-27 11:22:54 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-27 11:22:54 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-27 11:22:54 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-27 11:22:55 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-27 11:22:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@dc28d74, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12922d53, org.springframework.security.web.context.SecurityContextPersistenceFilter@5653429e, org.springframework.security.web.header.HeaderWriterFilter@53b60595, org.springframework.security.web.authentication.logout.LogoutFilter@a94c8e3, com.macro.mall.security.component.JwtAuthenticationTokenFilter@5c81ddab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8a0a1d1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6dd50380, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@f28ee19, org.springframework.security.web.session.SessionManagementFilter@538fb761, org.springframework.security.web.access.ExceptionTranslationFilter@16a89351, com.macro.mall.security.component.DynamicSecurityFilter@463a1f47, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d9016fe]
2025-06-27 11:22:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-27 11:22:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-27 11:22:57 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-27 11:22:57 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 23.516 seconds (JVM running for 24.117)
2025-06-27 11:22:58 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-27 11:22:58 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-27 11:22:58 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-27 11:23:05 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /]
2025-06-27 11:23:22 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-27 11:23:22 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-27 11:23:22 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-27 11:23:22 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-27 11:23:22 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-27 11:23:23 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-27 11:23:23 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-27 11:23:23 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-27 11:23:23.544(Timestamp), ************(String), null, null
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-27 11:23:24 [http-nio-8080-exec-8] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-27 11:23:25 [http-nio-8080-exec-8] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-27 11:23:25 [http-nio-8080-exec-8] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1750994602406,"spendTime":3164,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUwOTk0NjAzMjcxLCJleHAiOjE3NTE1OTk0MDN9.eU97rAZxnXN4lpn2rEMncqeP5Oa2HCg_iH6yiHSBMJLoXm19lEAoD8crXVCxwK5odJ7Mm-hpCWX0VvYrt7ZXDw"}}}
2025-06-27 11:23:50 [http-nio-8080-exec-2] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"商品一键上架SHEIN","startTime":1750994630626,"spendTime":3,"basePath":"http://localhost:8080","uri":"/shein/product/create","url":"http://localhost:8080/shein/product/create","method":"POST","ip":"************","parameter":{"sourceSystem":"OpenAPI"},"result":{"success":true,"message":"商品创建成功","sheinProductId":"SHEIN_PRODUCT_1750994630629"}}
2025-06-27 11:24:31 [http-nio-8080-exec-9] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"商品发布或编辑","startTime":1750994671681,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/publish-or-edit","url":"http://localhost:8080/shein/product/publish-or-edit","method":"POST","ip":"************","parameter":{"categoryId":13132,"productTypeId":9867,"supplierCode":"sc20241119002s2412100969","suitFlag":0,"multiLanguageNameList":[{"language":"en","name":"test name sc_eu20241127001"}],"productAttributeList":[{"attributeId":1001236},{"attributeId":160},{"attributeId":1000627},{"attributeId":1000411},{"attributeId":1000407},{"attributeId":1000462},{"attributeId":152},{"attributeId":1001518}],"siteList":[{"mainSite":"shein","subSiteList":["shein-fr"]}],"skcList":[{"supplierCode":"","imageInfo":{"imageInfoList":[{"imageSort":1,"imageType":1,"imageUrl":"https://imgdeal-test01.shein.com/images3_pi/2024/12/10/27/17338148602250996226.jpeg"},{"imageSort":2,"imageType":2,"imageUrl":"https://imgdeal-test01.shein.com/images3_pi/2024/12/10/27/17338148602250996226.jpeg"},{"imageSort":3,"imageType":5,"imageUrl":"https://imgdeal-test01.shein.com/images3_pi/2024/12/10/59/17338148663526671606.jpeg"}]},"saleAttribute":{"attributeId":27},"skuList":[{"supplierSku":"","costInfo":{"costPrice":"10.00","currency":"EUR"},"height":"5.00","length":"10.00","width":"10.00","weight":"10","mallState":1,"stockInfoList":[{"inventoryNum":10}]}]}]},"result":{"code":"0","msg":"OK","info":{"success":true,"spuName":"MM1750994671681","skcList":[{"skcName":"sMM17509946716817569","skuList":[{"skuCode":"Ic169a7p8oqu","supplierSku":"sc20241119002s2412100969ss17509946716810TEST1"}]}],"version":"SPMP1750994671681","extra":{}},"traceId":"trace_1750994671681"}}
2025-06-27 11:26:07 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-27 11:26:07 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-27 13:10:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-27 13:10:49 [main] INFO  com.macro.mall.MallAdminApplication - Starting MallAdminApplication using Java 17.0.13 on LilPear with PID 139520 (D:\project\Mall\mall-admin\target\classes started by LilPear2002 in D:\project\Mall)
2025-06-27 13:10:49 [main] DEBUG com.macro.mall.MallAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-27 13:10:49 [main] INFO  com.macro.mall.MallAdminApplication - The following 1 profile is active: "dev"
2025-06-27 13:10:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-27 13:10:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-27 13:10:58 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104 ms. Found 0 Redis repository interfaces.
2025-06-27 13:10:59 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'swaggerConfig' of type [com.macro.mall.config.SwaggerConfig$$EnhancerBySpringCGLIB$$9bcd9ae8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-27 13:10:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-27 13:10:59 [main] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.3.1] using APR version [1.7.4].
2025-06-27 13:10:59 [main] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-27 13:10:59 [main] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-27 13:10:59 [main] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 3.0.14 4 Jun 2024]
2025-06-27 13:10:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 13:10:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-27 13:10:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-27 13:11:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-27 13:11:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 10355 ms
2025-06-27 13:11:00 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-27 13:11:10 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-27 13:11:12 [main] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, create_time, name, url, description, category_id from ums_resource
2025-06-27 13:11:12 [main] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: 
2025-06-27 13:11:12 [main] DEBUG c.m.m.m.U.selectByExample - <==      Total: 31
2025-06-27 13:11:13 [main] WARN  c.m.m.s.c.DynamicSecurityFilter - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-06-27 13:11:13 [main] DEBUG c.m.m.s.c.JwtAuthenticationTokenFilter - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-06-27 13:11:14 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-27 13:11:14 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@12922d53, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@f28ee19, org.springframework.security.web.context.SecurityContextPersistenceFilter@16a89351, org.springframework.security.web.header.HeaderWriterFilter@2e214d39, org.springframework.security.web.authentication.logout.LogoutFilter@3dc5eb8f, com.macro.mall.security.component.JwtAuthenticationTokenFilter@3aa8c337, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@53b60595, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f9d47a3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c07deff, org.springframework.security.web.session.SessionManagementFilter@67c912d3, org.springframework.security.web.access.ExceptionTranslationFilter@69e58566, com.macro.mall.security.component.DynamicSecurityFilter@3c04ddda, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4f378d7e]
2025-06-27 13:11:15 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-27 13:11:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-27 13:11:15 [main] WARN  s.d.s.r.o.OperationImplicitParameterReader - Unable to interpret the implicit parameter configuration with dataType: integer, dataTypeClass: class java.lang.Void
2025-06-27 13:11:16 [main] INFO  com.macro.mall.MallAdminApplication - Started MallAdminApplication in 27.192 seconds (JVM running for 29.703)
2025-06-27 13:11:22 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-27 13:11:22 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-27 13:11:22 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-27 13:11:22 [http-nio-8080-exec-1] DEBUG c.m.m.s.c.DynamicSecurityFilter - Authorized public object filter invocation [GET /]
2025-06-27 13:11:38 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-27 13:11:38 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:38 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getResourceList - ==>  Preparing: SELECT ur.id id, ur.create_time createTime, ur.`name` `name`, ur.url url, ur.description description, ur.category_id categoryId FROM ums_admin_role_relation ar LEFT JOIN ums_role r ON ar.role_id = r.id LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id WHERE ar.admin_id = ? AND ur.id IS NOT NULL GROUP BY ur.id
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getResourceList - ==> Parameters: 20(Long)
2025-06-27 13:11:38 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getResourceList - <==      Total: 13
2025-06-27 13:11:38 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:39 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-27 13:11:39 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - ==>  Preparing: insert into ums_admin_login_log (admin_id, create_time, ip, address, user_agent) values (?, ?, ?, ?, ?)
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - ==> Parameters: 20(Long), 2025-06-27 13:11:39.521(Timestamp), ************(String), null, null
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert - <==    Updates: 1
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - ==>  Preparing: SELECT LAST_INSERT_ID()
2025-06-27 13:11:39 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - ==> Parameters: 
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.insert!selectKey - <==      Total: 1
2025-06-27 13:11:40 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==>  Preparing: select id, username, password, icon, email, nick_name, note, create_time, login_time, status from ums_admin WHERE ( username = ? )
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - ==> Parameters: testls(String)
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.U.selectByExample - <==      Total: 1
2025-06-27 13:11:40 [http-nio-8080-exec-6] ERROR c.m.m.s.aspect.RedisCacheAspect - Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost/<unresolved>:6379
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - ==>  Preparing: select r.* from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id where ar.admin_id = ?
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - ==> Parameters: 20(Long)
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.d.U.getRoleList - <==      Total: 1
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==>  Preparing: INSERT INTO admin_api_log(username, api_path, http_method, operation, request_param, ip_address, create_time) VALUES(?, ?, ?, ?, ?, ?, NOW())
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - ==> Parameters: testls(String), /admin/login(String), POST(String), 用户登陆(String), {"username":"testls","password":"testls"}(String), 0:0:0:0:0:0:0:1(String)
2025-06-27 13:11:40 [http-nio-8080-exec-6] DEBUG c.m.m.m.AdminApiLogMapper.insert - <==    Updates: 1
2025-06-27 13:11:40 [http-nio-8080-exec-6] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"登录以后返回token","username":"testls","startTime":1751001098057,"spendTime":2785,"basePath":"http://localhost:8080","uri":"/admin/login","url":"http://localhost:8080/admin/login","method":"POST","ip":"************","parameter":{"username":"testls","password":"testls"},"result":{"code":200,"message":"操作成功","data":{"tokenHead":"Bearer ","roles":["零售商"],"defaultRoute":"/pms/product","token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0bHMiLCJjcmVhdGVkIjoxNzUxMDAxMDk5MDQ3LCJleHAiOjE3NTE2MDU4OTl9.baHG3zbDgkvvbIZPLnD4i2D0j1g2GvWCQDy3H2OQWh1gn6lPcOCDo9SGdvMyiTAoVv8foyL5ftLcmTR-RGGndw"}}}
2025-06-27 13:11:57 [http-nio-8080-exec-7] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品审核状态","startTime":1751001117981,"spendTime":3,"basePath":"http://localhost:8080","uri":"/shein/product/query-document-state","url":"http://localhost:8080/shein/product/query-document-state","method":"POST","ip":"************","parameter":{"spuList":[{"spuName":"MM2404076986","version":"SPMP240407262081729"}]},"result":{"code":"0","msg":"OK","info":{"data":[{"spuName":"MM2404076986","version":"SPMP240407262081729","skcList":[{"skcName":"sMM24040769866671","documentSn":"SPMPA321751001117984","documentState":3,"failedReason":[{"language":"zh-cn","content":"禁忌:frankie test12;1111111"},{"language":"zh-cn","content":"上新管控:测试上新管控驳回·1;1111111"}]}]}],"meta":{"count":1}},"traceId":"trace_1751001117984"}}
2025-06-27 13:12:04 [http-nio-8080-exec-8] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品审核状态","startTime":1751001124590,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/query-document-state","url":"http://localhost:8080/shein/product/query-document-state","method":"POST","ip":"************","parameter":{"spuList":[{"version":"SPMP240407262081729"}]},"result":{"code":"-1","msg":"SPU名称不能为空","traceId":"trace_1751001124590"}}
2025-06-27 13:12:07 [http-nio-8080-exec-1] INFO  c.macro.mall.common.log.WebLogAspect - {"description":"查询商品审核状态","startTime":1751001127491,"spendTime":0,"basePath":"http://localhost:8080","uri":"/shein/product/query-document-state","url":"http://localhost:8080/shein/product/query-document-state","method":"POST","ip":"************","parameter":{"spuList":[{"spuName":"MM2404076986","version":"SPMP240407262081729"}]},"result":{"code":"0","msg":"OK","info":{"data":[{"spuName":"MM2404076986","version":"SPMP240407262081729","skcList":[{"skcName":"sMM24040769866671","documentSn":"SPMPA321751001127491","documentState":3,"failedReason":[{"language":"zh-cn","content":"禁忌:frankie test12;1111111"},{"language":"zh-cn","content":"上新管控:测试上新管控驳回·1;1111111"}]}]}],"meta":{"count":1}},"traceId":"trace_1751001127491"}}
2025-06-27 13:13:10 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-27 13:13:10 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
