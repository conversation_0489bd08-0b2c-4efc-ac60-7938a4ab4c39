<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsPrefrenceAreaMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsPrefrenceArea">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.CmsPrefrenceArea">
    <result column="pic" jdbcType="VARBINARY" property="pic" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, sub_title, sort, show_status
  </sql>
  <sql id="Blob_Column_List">
    pic
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.CmsPrefrenceAreaExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_prefrence_area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsPrefrenceAreaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_prefrence_area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_prefrence_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_prefrence_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsPrefrenceAreaExample">
    delete from cms_prefrence_area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsPrefrenceArea">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_prefrence_area (name, sub_title, sort, 
      show_status, pic)
    values (#{name,jdbcType=VARCHAR}, #{subTitle,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{showStatus,jdbcType=INTEGER}, #{pic,jdbcType=VARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsPrefrenceArea">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_prefrence_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="pic != null">
        pic,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsPrefrenceAreaExample" resultType="java.lang.Long">
    select count(*) from cms_prefrence_area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_prefrence_area
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.subTitle != null">
        sub_title = #{record.subTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.pic != null">
        pic = #{record.pic,jdbcType=VARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update cms_prefrence_area
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      sub_title = #{record.subTitle,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      pic = #{record.pic,jdbcType=VARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_prefrence_area
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      sub_title = #{record.subTitle,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      show_status = #{record.showStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsPrefrenceArea">
    update cms_prefrence_area
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        sub_title = #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        pic = #{pic,jdbcType=VARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.CmsPrefrenceArea">
    update cms_prefrence_area
    set name = #{name,jdbcType=VARCHAR},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      show_status = #{showStatus,jdbcType=INTEGER},
      pic = #{pic,jdbcType=VARBINARY}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsPrefrenceArea">
    update cms_prefrence_area
    set name = #{name,jdbcType=VARCHAR},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      show_status = #{showStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>