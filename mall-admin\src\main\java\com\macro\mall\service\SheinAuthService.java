package com.macro.mall.service;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dto.SheinAuthCallbackDTO;
import com.macro.mall.dto.SheinAuthPrepareDTO;
import com.macro.mall.dto.SheinGetTokenDTO;
import com.macro.mall.vo.AuthResultVo;

public interface SheinAuthService {
    CommonResult<SheinAuthPrepareDTO> prepareAuthLink();

    CommonResult handleCallback(SheinAuthCallbackDTO dto);
}
