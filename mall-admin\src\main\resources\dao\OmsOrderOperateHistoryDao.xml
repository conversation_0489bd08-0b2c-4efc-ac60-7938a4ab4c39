<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.OmsOrderOperateHistoryDao">
    <insert id="insertList">
        INSERT INTO oms_order_operate_history (order_id, operate_man, create_time, order_status, note) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.orderId},
            #{item.operateMan},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.orderStatus},
            #{item.note})
        </foreach>
    </insert>
</mapper>