package com.macro.mall.service.impl;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dao.SheinProductConfigDao;
import com.macro.mall.Sheindto.SheinProductConfig;
import com.macro.mall.service.SheinProductConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * SHEIN商品配置Service实现类
 */
@Slf4j
@Service
public class SheinProductConfigServiceImpl implements SheinProductConfigService {

    @Resource
    private SheinProductConfigDao sheinProductConfigDao;

    @Override
    public CommonResult<SheinProductConfig> createConfig(SheinProductConfig config) {
        try {
            // 参数验证
            if (config == null) {
                return CommonResult.failed("配置信息不能为空");
            }
            if (config.getProductId() == null) {
                return CommonResult.failed("商品ID不能为空");
            }

            // 检查是否已存在该商品的配置
            SheinProductConfig existingConfig = sheinProductConfigDao.selectByProductId(config.getProductId());
            if (existingConfig != null) {
                return CommonResult.failed("该商品已存在配置，请使用更新接口");
            }

            // 设置创建时间和更新时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            config.setCreatedAt(currentTime);
            config.setUpdatedAt(LocalDateTime.now());

            // 插入数据
            int result = sheinProductConfigDao.insert(config);
            if (result > 0) {
                log.info("创建SHEIN商品配置成功，商品ID: {}", config.getProductId());
                return CommonResult.success(config, "创建成功");
            } else {
                return CommonResult.failed("创建失败");
            }
        } catch (Exception e) {
            log.error("创建SHEIN商品配置失败", e);
            return CommonResult.failed("创建失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<SheinProductConfig> getConfigByProductId(Long productId) {
        try {
            if (productId == null) {
                return CommonResult.failed("商品ID不能为空");
            }

            SheinProductConfig config = sheinProductConfigDao.selectByProductId(productId);
            if (config != null) {
                return CommonResult.success(config, "查询成功");
            } else {
                return CommonResult.failed("未找到该商品的配置信息");
            }
        } catch (Exception e) {
            log.error("查询SHEIN商品配置失败，商品ID: {}", productId, e);
            return CommonResult.failed("查询失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<SheinProductConfig> updateConfigByProductId(Long productId, SheinProductConfig config) {
        try {
            if (productId == null) {
                return CommonResult.failed("商品ID不能为空");
            }
            if (config == null) {
                return CommonResult.failed("配置信息不能为空");
            }

            // 检查配置是否存在
            SheinProductConfig existingConfig = sheinProductConfigDao.selectByProductId(productId);
            if (existingConfig == null) {
                return CommonResult.failed("未找到该商品的配置信息");
            }

            // 设置商品ID和更新时间
            config.setProductId(productId);
            config.setUpdatedAt(LocalDateTime.now());

            // 更新数据
            int result = sheinProductConfigDao.updateByProductId(config);
            if (result > 0) {
                // 查询更新后的数据
                SheinProductConfig updatedConfig = sheinProductConfigDao.selectByProductId(productId);
                log.info("更新SHEIN商品配置成功，商品ID: {}", productId);
                return CommonResult.success(updatedConfig, "更新成功");
            } else {
                return CommonResult.failed("更新失败");
            }
        } catch (Exception e) {
            log.error("更新SHEIN商品配置失败，商品ID: {}", productId, e);
            return CommonResult.failed("更新失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> deleteConfigByProductId(Long productId) {
        try {
            if (productId == null) {
                return CommonResult.failed("商品ID不能为空");
            }

            // 检查配置是否存在
            SheinProductConfig existingConfig = sheinProductConfigDao.selectByProductId(productId);
            if (existingConfig == null) {
                return CommonResult.failed("未找到该商品的配置信息");
            }

            // 删除数据
            int result = sheinProductConfigDao.deleteByProductId(productId);
            if (result > 0) {
                log.info("删除SHEIN商品配置成功，商品ID: {}", productId);
                return CommonResult.success("删除成功");
            } else {
                return CommonResult.failed("删除失败");
            }
        } catch (Exception e) {
            log.error("删除SHEIN商品配置失败，商品ID: {}", productId, e);
            return CommonResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<SheinProductConfig>> listConfigs(Integer pageNum, Integer pageSize) {
        try {
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;

            // 查询数据
            List<SheinProductConfig> configs = sheinProductConfigDao.selectConfigsWithPaging(offset, pageSize);
            Long total = sheinProductConfigDao.countConfigs();

            log.info("分页查询SHEIN商品配置成功，页码: {}, 每页数量: {}, 总数: {}", pageNum, pageSize, total);
            return CommonResult.success(configs, "查询成功，总数：" + total);
        } catch (Exception e) {
            log.error("分页查询SHEIN商品配置失败", e);
            return CommonResult.failed("查询失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<List<SheinProductConfig>> getAllConfigs() {
        try {
            List<SheinProductConfig> configs = sheinProductConfigDao.selectAllConfigs();
            log.info("查询所有SHEIN商品配置成功，数量: {}", configs.size());
            return CommonResult.success(configs, "查询成功");
        } catch (Exception e) {
            log.error("查询所有SHEIN商品配置失败", e);
            return CommonResult.failed("查询失败：" + e.getMessage());
        }
    }
}
