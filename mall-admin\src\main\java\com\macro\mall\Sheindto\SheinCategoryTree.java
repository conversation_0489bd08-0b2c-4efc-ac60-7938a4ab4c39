package com.macro.mall.Sheindto;

import lombok.Data;

import java.util.List;

/**
 * SHEIN类目树响应DTO
 */
@Data
public class SheinCategoryTree {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    @Data
    public static class Info {
        /**
         * 类目数据列表
         */
        private List<Category> data;
        
        /**
         * 元数据信息
         */
        private Meta meta;
    }
    
    @Data
    public static class Category {
        /**
         * 类目ID
         */
        private Long categoryId;
        
        /**
         * 产品类型ID
         */
        private Long productTypeId;
        
        /**
         * 父类目ID
         */
        private Long parentCategoryId;
        
        /**
         * 类目名称
         */
        private String categoryName;
        
        /**
         * 是否为最后一级类目
         */
        private Boolean lastCategory;
        
        /**
         * 子类目列表
         */
        private List<Category> children;
    }
    
    @Data
    public static class Meta {
        /**
         * 总数量
         */
        private Integer count;
        
        /**
         * 自定义对象
         */
        private Object customObj;
    }
}
