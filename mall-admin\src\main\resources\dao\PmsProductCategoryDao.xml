<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsProductCategoryDao">
    <resultMap id="listWithChildrenMap" type="com.macro.mall.dto.PmsProductCategoryWithChildrenItem"
               extends="com.macro.mall.mapper.PmsProductCategoryMapper.BaseResultMap">
        <collection property="children" resultMap="com.macro.mall.mapper.PmsProductCategoryMapper.BaseResultMap"
                    columnPrefix="child_"></collection>
    </resultMap>
    <select id="listWithChildren" resultMap="listWithChildrenMap">
        select
        c1.id,
        c1.name,
        c1.warehouse_id,
        c2.id   child_id,
        c2.name child_name,
        c2.warehouse_id child_warehouse_id
        from pms_product_category c1
        left join pms_product_category c2
        on c1.id = c2.parent_id
        and c2.product_count > 0
        and c2.product_count is not null
        where c1.parent_id = 0
        and c1.product_count > 0
        and c1.product_count is not null
    </select>
</mapper>