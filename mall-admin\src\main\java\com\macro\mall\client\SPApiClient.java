package com.macro.mall.client;

import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.service.AmazonTokenRefreshServer;
import com.macro.mall.util.AwsSignerV4;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class SPApiClient {

    @Value("${amazon.aws_access_key}")
    private String awsAccessKey;

    @Value("${amazon.aws_secret_key}")
    private String awsSecretKey;

    @Value("${amazon.region}")
    private String region;

    @Autowired
    private AmazonTokenRefreshServer amazonTokenRefreshServer;

    @Autowired
    private UmsMemberMapper umsMemberMapper;

    private final String host = "sellingpartnerapi-na.amazon.com";
    private final String endpoint = "https://" + host;

    private final String service = "execute-api";
    private final RestTemplate restTemplate = new RestTemplate();

    public String doPost(Long memberId, String apiPath, String payload) throws Exception {
        return send(memberId, "POST", apiPath, "", payload);
    }

    public String doGet(Long memberId, String apiPath, String queryString) throws Exception {
        return send(memberId, "GET", apiPath, queryString, "");
    }

    private String send(Long memberId, String method, String apiPath, String queryString, String payload) throws Exception {
        UmsMember member = umsMemberMapper.selectByPrimaryKey(memberId);
        String accessToken = amazonTokenRefreshServer.getValidAccessToken(member);

        // 准备 headers
        Map<String, String> headers = new HashMap<>();
        headers.put("host", host);
        headers.put("content-type", "application/json");
        headers.put("x-amz-access-token", accessToken);

        AwsSignerV4 signer = new AwsSignerV4(awsAccessKey, awsSecretKey, region, service);
        Map<String, String> signedHeaders = signer.sign(method, apiPath, queryString, headers, payload);

        // 构造URL
        String url = endpoint + apiPath;
        if (!queryString.isEmpty()) {
            url += "?" + queryString;
        }

        HttpHeaders httpHeaders = new HttpHeaders();
        signedHeaders.forEach(httpHeaders::set);

        HttpEntity<String> entity = new HttpEntity<>(payload, httpHeaders);

        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.resolve(method),
                entity,
                String.class
        );

        return response.getBody();
    }

    public String doPut(Long memberId, String apiPath, String payload) throws Exception {
        return send(memberId, "PUT", apiPath, "", payload);
    }
}

