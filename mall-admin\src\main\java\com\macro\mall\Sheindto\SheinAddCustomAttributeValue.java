package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SHEIN添加自定义属性值响应DTO
 */
@Data
public class SheinAddCustomAttributeValue {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 供应商ID
         */
        @JsonProperty("supplier_id")
        private Long supplierId;
        
        /**
         * 供应商来源
         */
        @JsonProperty("supplier_source")
        private Integer supplierSource;
        
        /**
         * 分类ID
         */
        @JsonProperty("category_id")
        private Long categoryId;
        
        /**
         * 属性ID
         */
        @JsonProperty("attribute_id")
        private Long attributeId;
        
        /**
         * 属性值ID
         */
        @JsonProperty("attribute_value_id")
        private Long attributeValueId;
        
        /**
         * 属性值名称
         */
        @JsonProperty("attribute_value_name")
        private String attributeValueName;
        
        /**
         * 属性值多语种信息
         */
        @JsonProperty("attribute_value_multi_arr")
        private List<AttributeValueMulti> attributeValueMultiArr;
    }
    
    @Data
    public static class AttributeValueMulti {
        /**
         * 属性值多语种名称
         */
        @JsonProperty("attribute_value_name_multi")
        private String attributeValueNameMulti;
        
        /**
         * 语种
         */
        @JsonProperty("language")
        private String language;
    }
}
