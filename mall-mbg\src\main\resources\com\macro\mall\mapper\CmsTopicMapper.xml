<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsTopicMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsTopic">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="attend_count" jdbcType="INTEGER" property="attendCount" />
    <result column="attention_count" jdbcType="INTEGER" property="attentionCount" />
    <result column="read_count" jdbcType="INTEGER" property="readCount" />
    <result column="award_name" jdbcType="VARCHAR" property="awardName" />
    <result column="attend_type" jdbcType="VARCHAR" property="attendType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.CmsTopic">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, name, create_time, start_time, end_time, attend_count, attention_count, 
    read_count, award_name, attend_type
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.CmsTopicExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsTopicExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_topic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_topic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsTopicExample">
    delete from cms_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsTopic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic (category_id, name, create_time, 
      start_time, end_time, attend_count, 
      attention_count, read_count, award_name, 
      attend_type, content)
    values (#{categoryId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{attendCount,jdbcType=INTEGER}, 
      #{attentionCount,jdbcType=INTEGER}, #{readCount,jdbcType=INTEGER}, #{awardName,jdbcType=VARCHAR}, 
      #{attendType,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsTopic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="attendCount != null">
        attend_count,
      </if>
      <if test="attentionCount != null">
        attention_count,
      </if>
      <if test="readCount != null">
        read_count,
      </if>
      <if test="awardName != null">
        award_name,
      </if>
      <if test="attendType != null">
        attend_type,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attendCount != null">
        #{attendCount,jdbcType=INTEGER},
      </if>
      <if test="attentionCount != null">
        #{attentionCount,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        #{readCount,jdbcType=INTEGER},
      </if>
      <if test="awardName != null">
        #{awardName,jdbcType=VARCHAR},
      </if>
      <if test="attendType != null">
        #{attendType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsTopicExample" resultType="java.lang.Long">
    select count(*) from cms_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_topic
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.attendCount != null">
        attend_count = #{record.attendCount,jdbcType=INTEGER},
      </if>
      <if test="record.attentionCount != null">
        attention_count = #{record.attentionCount,jdbcType=INTEGER},
      </if>
      <if test="record.readCount != null">
        read_count = #{record.readCount,jdbcType=INTEGER},
      </if>
      <if test="record.awardName != null">
        award_name = #{record.awardName,jdbcType=VARCHAR},
      </if>
      <if test="record.attendType != null">
        attend_type = #{record.attendType,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update cms_topic
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      attend_count = #{record.attendCount,jdbcType=INTEGER},
      attention_count = #{record.attentionCount,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      award_name = #{record.awardName,jdbcType=VARCHAR},
      attend_type = #{record.attendType,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_topic
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      attend_count = #{record.attendCount,jdbcType=INTEGER},
      attention_count = #{record.attentionCount,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      award_name = #{record.awardName,jdbcType=VARCHAR},
      attend_type = #{record.attendType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsTopic">
    update cms_topic
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attendCount != null">
        attend_count = #{attendCount,jdbcType=INTEGER},
      </if>
      <if test="attentionCount != null">
        attention_count = #{attentionCount,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        read_count = #{readCount,jdbcType=INTEGER},
      </if>
      <if test="awardName != null">
        award_name = #{awardName,jdbcType=VARCHAR},
      </if>
      <if test="attendType != null">
        attend_type = #{attendType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.CmsTopic">
    update cms_topic
    set category_id = #{categoryId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      attend_count = #{attendCount,jdbcType=INTEGER},
      attention_count = #{attentionCount,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      award_name = #{awardName,jdbcType=VARCHAR},
      attend_type = #{attendType,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsTopic">
    update cms_topic
    set category_id = #{categoryId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      attend_count = #{attendCount,jdbcType=INTEGER},
      attention_count = #{attentionCount,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      award_name = #{awardName,jdbcType=VARCHAR},
      attend_type = #{attendType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>