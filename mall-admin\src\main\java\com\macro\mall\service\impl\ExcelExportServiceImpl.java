package com.macro.mall.service.impl;

import com.macro.mall.dto.PmsProductQueryParam;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.service.ExcelExportService;
import com.macro.mall.service.PmsProductService;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ExcelExportServiceImpl implements ExcelExportService {
    @Autowired
    private PmsProductService productService;

    @Override
    public ByteArrayOutputStream exportAllProductsAsExcel(PmsProductQueryParam queryParam) {
        // 1. 拉取所有数据（分页合并）
        List<PmsProduct> allProducts = new ArrayList<>();
        int pageSize = 100;
        int pageNum = 1;
        while (true) {
            List<PmsProduct> page = productService.list(queryParam, pageSize, pageNum);
            if (page == null || page.isEmpty()) break;
            allProducts.addAll(page);
            if (page.size() < pageSize) break;
            pageNum++;
        }

        // 2. 生成 Excel 文件
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("商品列表");

        String[] headers = {
                "商品名称", "商品编码", "商品SN", "商品Code", "图像", "副标题", "一级分类", "二级分类",
                "价格", "库存", "长（cm）", "宽（cm）", "高（cm）", "体积（cm³）", "重量（g）",
                "所在地", "是否新品", "是否推荐", "上架时间"
        };

        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }

        int rowNum = 1;
        for (PmsProduct p : allProducts) {
            Row row = sheet.createRow(rowNum);

            row.createCell(0).setCellValue(p.getName());
            row.createCell(1).setCellValue(p.getId());
            row.createCell(2).setCellValue(p.getProductSn());
            row.createCell(3).setCellValue(p.getProductCode());

            row.createCell(5).setCellValue(p.getSubTitle());
            row.createCell(6).setCellValue(p.getProductCategoryName());
            row.createCell(7).setCellValue(p.getProductCateUplevelName());
            row.createCell(8).setCellValue(p.getCurrency() + p.getPrice());
            row.createCell(9).setCellValue(p.getStock());

            row.createCell(10).setCellValue(p.getLength() != null ? p.getLength().toString() : "");
            row.createCell(11).setCellValue(p.getWidth() != null ? p.getWidth().toString() : "");
            row.createCell(12).setCellValue(p.getHeight() != null ? p.getHeight().toString() : "");
            row.createCell(13).setCellValue(p.getSize() != null ? p.getSize().toString() : "");
            row.createCell(14).setCellValue(p.getWeight() != null ? p.getWeight().toString() : "");

            row.createCell(15).setCellValue(Optional.ofNullable(p.getLocation()).orElse(""));
            row.createCell(16).setCellValue(p.getNewStatus() == 1 ? "是" : "否");
            row.createCell(17).setCellValue(p.getRecommandStatus() == 1 ? "是" : "否");
            row.createCell(18).setCellValue(p.getShelfTime());

            // 插入图片
            try {
                byte[] imageBytes = downloadImage(p.getPic());
                if (imageBytes != null) {
                    int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
                    CreationHelper helper = workbook.getCreationHelper();
                    Drawing<?> drawing = sheet.createDrawingPatriarch();
                    ClientAnchor anchor = helper.createClientAnchor();
                    anchor.setCol1(4); // 图片列
                    anchor.setRow1(rowNum);
                    Picture pict = drawing.createPicture(anchor, pictureIdx);
                    pict.resize(1.0, 1.0);
                }
            } catch (Exception e) {
                System.err.println("图片加载失败: " + p.getPic());
            }

            rowNum++;
        }

        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            workbook.write(out);
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException("写入 Excel 失败", e);
        }

        return out;
    }

    private byte[] downloadImage(String imageUrl) throws IOException {
        if (StringUtils.isBlank(imageUrl)) return null;
        URL url = new URL(imageUrl);
        try (InputStream is = url.openStream();
             ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

            byte[] data = new byte[1024];
            int nRead;
            while ((nRead = is.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            return buffer.toByteArray();
        }
    }
}
