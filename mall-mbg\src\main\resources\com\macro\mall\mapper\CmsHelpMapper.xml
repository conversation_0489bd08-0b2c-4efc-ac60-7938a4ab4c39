<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsHelpMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsHelp">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="read_count" jdbcType="INTEGER" property="readCount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.macro.mall.model.CmsHelp">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, icon, title, show_status, create_time, read_count
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.macro.mall.model.CmsHelpExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_help
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsHelpExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_help
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cms_help
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_help
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsHelpExample">
    delete from cms_help
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsHelp">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_help (category_id, icon, title, 
      show_status, create_time, read_count, 
      content)
    values (#{categoryId,jdbcType=BIGINT}, #{icon,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{showStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{readCount,jdbcType=INTEGER}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsHelp">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_help
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="readCount != null">
        read_count,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readCount != null">
        #{readCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsHelpExample" resultType="java.lang.Long">
    select count(*) from cms_help
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_help
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.readCount != null">
        read_count = #{record.readCount,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update cms_help
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      icon = #{record.icon,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      read_count = #{record.readCount,jdbcType=INTEGER},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_help
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      icon = #{record.icon,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      read_count = #{record.readCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsHelp">
    update cms_help
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readCount != null">
        read_count = #{readCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.macro.mall.model.CmsHelp">
    update cms_help
    set category_id = #{categoryId,jdbcType=BIGINT},
      icon = #{icon,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      show_status = #{showStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      read_count = #{readCount,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsHelp">
    update cms_help
    set category_id = #{categoryId,jdbcType=BIGINT},
      icon = #{icon,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      show_status = #{showStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      read_count = #{readCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>