<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.CmsSubjectProductRelationDao">
    <insert id="insertList">
        insert into cms_subject_product_relation (subject_id, product_id) values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.subjectId,jdbcType=BIGINT},
            #{item.productId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>