package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * SHEIN本地图片上传响应DTO
 */
@Data
public class SheinUploadPic {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 图片链接
         */
        @JsonProperty("image_url")
        private String imageUrl;
        
        /**
         * 图片宽度
         */
        @JsonProperty("width")
        private Integer width;
        
        /**
         * 图片高度
         */
        @JsonProperty("height")
        private Integer height;
        
        /**
         * 图片大小（字节）
         */
        @JsonProperty("size")
        private Integer size;
        
        /**
         * 图片类型
         */
        @JsonProperty("image_hex_type")
        private String imageHexType;
    }
}
