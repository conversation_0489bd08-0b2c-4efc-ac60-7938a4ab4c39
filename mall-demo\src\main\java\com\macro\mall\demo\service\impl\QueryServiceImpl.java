package com.macro.mall.demo.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.macro.mall.demo.mapper.UmsEuexMapper;
import com.macro.mall.demo.model.UmsEuex;
import com.macro.mall.demo.model.request.*;
import com.macro.mall.demo.service.PrintService;
import com.macro.mall.demo.service.QueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class QueryServiceImpl implements QueryService {
    private static final Logger log = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Value("${k5.Verify.Clientid}")
    private String k5Clientid;
    @Value("${k5.Verify.Token}")
    private String k5Token;
    @Value("${k5.Verify.ClientidUPS}")
    private String k5ClientidUPS;
    @Value("${k5.Verify.TokenUPS}")
    private String k5TokenUPS;
    @Value("${k5.url}")
    private String k5url;

    @Autowired
    private UmsEuexMapper umsEuexMapper;
    @Autowired
    private TransLogServiceImpl transLogService;
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getActiveChannels(QueryWarehouseRequest request) throws Exception {
        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        // 验证 client_id 和 token
        UmsEuex umsEuex = umsEuexMapper.findByClientIdAndToken(request.getVerify());
        if (umsEuex == null) {
            throw new Exception("Invalid ClientId or Token");
        }

        // 创建 ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        // **记录修改前的 JSON 数据**
        QueryWarehouseRequest originalRequest = objectMapper.readValue(objectMapper.writeValueAsString(request), QueryWarehouseRequest.class);

        log.info("替换为我们的 client_id 和 token");
        String clientId = request.getVerify().getClientId();

        // 替换为我们的 client_id 和 token
        request.getVerify().setClientId(k5Clientid);
        request.getVerify().setToken(k5Token);

        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        String response = restTemplate.postForObject(k5url+"searchStartChannel", request, String.class);

        // 提取 statusCode
        String statusCode = transLogService.extractStatusCode(response);

        // **保存修改前后的请求数据**
        transLogService.saveToFile("searchStartChannel", originalRequest, request, response);

        // 记录日志
        transLogService.logRequest(clientId, "/searchStartChannel", response, statusCode);

        return response;
    }

    @Override
    public String getInsuranceTypes(QueryInsuranceRequest request) throws Exception {
        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        // 验证 client_id 和 token
        UmsEuex umsEuex = umsEuexMapper.findByClientIdAndToken(request.getVerify());
        if (umsEuex == null) {
            throw new Exception("Invalid ClientId or Token");
        }

        // 创建 ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        // **记录修改前的 JSON 数据**
        QueryInsuranceRequest originalRequest = objectMapper.readValue(objectMapper.writeValueAsString(request), QueryInsuranceRequest.class);

        log.info("替换为我们的 client_id 和 token");
        String clientId = request.getVerify().getClientId();

        // 替换为我们的 client_id 和 token
        request.getVerify().setClientId(k5Clientid);
        request.getVerify().setToken(k5Token);

        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        String response = restTemplate.postForObject(k5url+"searchInsuranceType", request, String.class);

        // 提取 statusCode
        String statusCode = transLogService.extractStatusCode(response);

        // **保存修改前后的请求数据**
        transLogService.saveToFile("searchInsuranceType", originalRequest, request, response);

        // 记录日志
        transLogService.logRequest(clientId, "/searchInsuranceType", response, statusCode);

        return response;
    }

    @Override
    public String calculatePrice(QueryPriceRequest request) throws Exception {
        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        // 验证 client_id 和 token
        UmsEuex umsEuex = umsEuexMapper.findByClientIdAndToken(request.getVerify());
        if (umsEuex == null) {
            throw new Exception("Invalid ClientId or Token");
        }

        // 创建 ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        // **记录修改前的 JSON 数据**
        QueryPriceRequest originalRequest = objectMapper.readValue(objectMapper.writeValueAsString(request), QueryPriceRequest.class);

        log.info("替换为我们的 client_id 和 token");
        String clientId = request.getVerify().getClientId();

        // 替换为我们的 client_id 和 token
        request.getVerify().setClientId(k5Clientid);
        request.getVerify().setToken(k5Token);

        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        String response = restTemplate.postForObject(k5url+"searchPrice", request, String.class);

        // 提取 statusCode
        String statusCode = transLogService.extractStatusCode(response);

        // **保存修改前后的请求数据**
        transLogService.saveToFile("searchPrice", originalRequest, request, response);

        // 记录日志
        transLogService.logRequest(clientId, "/searchPrice", response, statusCode);

        return response;
    }

    @Override
    public String getTrackingInfo(QueryTrackingRequest request) throws Exception {
        log.info("Clientid:{}", request.getVerify().getClientId());
        log.info("Token:{}", request.getVerify().getToken());

        // 验证 client_id 和 token
        UmsEuex umsEuex = umsEuexMapper.findByClientIdAndToken(request.getVerify());
        if (umsEuex == null) {
            throw new Exception("Invalid ClientId or Token");
        }

        // 创建 ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        // **记录修改前的 JSON 数据**
        QueryTrackingRequest originalRequest = objectMapper.readValue(objectMapper.writeValueAsString(request), QueryTrackingRequest.class);

        String clientId = request.getVerify().getClientId();

        if("UPS_WLY_Be".equals(request.getChannelCode()) || "UPS_DE_Bet".equals(request.getChannelCode())) {
            log.info("替换为我们的 client_idUPS 和 tokenUPS");
            request.getVerify().setClientId(k5ClientidUPS);
            request.getVerify().setToken(k5TokenUPS);

            log.info("Clientid:{}", request.getVerify().getClientId());
            log.info("Token:{}", request.getVerify().getToken());
        }
        else {
            log.info("替换为我们的 client_id 和 token");
            request.getVerify().setClientId(k5Clientid);
            request.getVerify().setToken(k5Token);

            log.info("Clientid:{}", request.getVerify().getClientId());
            log.info("Token:{}", request.getVerify().getToken());
        }

        String response = restTemplate.postForObject(k5url+"searchTrack", request, String.class);

        // 提取 statusCode
        String statusCode = transLogService.extractStatusCode(response);

        // **保存修改前后的请求数据**
        transLogService.saveToFile("searchTrack", originalRequest, request, response);

        // 记录日志
        transLogService.logRequest(clientId, "/searchTrack", response, statusCode);

        return response;
    }
}
