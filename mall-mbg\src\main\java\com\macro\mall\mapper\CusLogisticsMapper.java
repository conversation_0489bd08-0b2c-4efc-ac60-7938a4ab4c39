package com.macro.mall.mapper;

import com.macro.mall.model.CusBaseLogistics;
import com.macro.mall.model.CusLogistics;
import com.macro.mall.model.CusLogisticsHistory;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

public interface CusLogisticsMapper {

    CusLogistics selectByWaybillAndCustomerOrder(String waybillNumber, String customerOrderNumber, String fwTrackingNumber, String username);

    void updateByWaybillAndCustomerOrder(CusLogistics logistics);

    void insertLogistics(CusLogistics logistics);

    // 根据ID查找物流记录
    @Select("SELECT * FROM logistics_records WHERE id = #{id}")
    CusLogistics findById(@Param("id") Integer containerNumber);

    // 更新 customs_clearance_materials 字段
    @Update("UPDATE logistics_records SET customs_clearance_materials = #{filePath} WHERE id = #{id}")
    void updateCustomsClearanceMaterials(@Param("id") Integer id, @Param("filePath") String filePath);
    @Update("UPDATE logistics_records SET customs_clearance_result = #{filePath} WHERE id = #{id}")
    void updateCustomsClearanceResult(@Param("id") Integer id, @Param("filePath") String filePath);


    int updateNoteByKeys(@Param("id") Integer id,
                         @Param("note") String note,
                         @Param("trackUpdateTime") Timestamp trackUpdateTime);

    CusLogistics querySingleLogistics(@Param("field") String field,
                                      @Param("username") String username,
                                      @Param("id") Integer id);

    CusLogistics ROOTquerySingleLogistics(@Param("id") Integer id);

    boolean getLogisticsId(@Param("id") Integer id);

    void insertLogisticsHistory(Integer id, String note, Timestamp trackUpdateTime, String username, String role);

    List<CusLogisticsHistory> getLogisticsHistoryByLogisticsId(@Param("logisticsId") Long logisticsId);

    List<CusBaseLogistics> selectAllLogistics(@Param("field")String field, @Param("username") String username, @Param("startTime") Long startTimestamp, @Param("endTime") Long endTimestamp, @Param("waybillNumber") String waybillNumber);

    List<CusBaseLogistics> ROOTselectAllLogistics(@Param("startTime") Long startTimestamp, @Param("endTime") Long endTimestamp, @Param("waybillNumber") String waybillNumber);

    int updateStatusByIds(List<Integer> ids, String status);

    int deleteByIds(List<Integer> ids);


}
