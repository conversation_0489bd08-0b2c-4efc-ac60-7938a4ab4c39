package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * SHEIN查询是否支持自定义属性值请求DTO
 */
@Data
public class SheinCustomAttributePermissionRequest {
    
    /**
     * 类目ID列表（必填）
     */
    @NotEmpty(message = "类目ID列表不能为空")
    @JsonProperty("category_id_list")
    private List<Long> categoryIdList;
}
