server:
  port: 8085

spring:
  datasource:
    url: ***********************************************************************************************************
    username: reader
    password: 123456
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 #最小空闲连接数
      max-active: 20 #最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" #不统计这些请求数据
      stat-view-servlet: #访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid

  data:
    mongodb:
      host: mongo
      port: 27017
      database: mall-port

  redis:
    host: redis # Redis服务器地址
    database: 0 # Redis数据库索引（默认为0）
    port: 6379 # Redis服务器连接端口
    password: # Redis服务器连接密码（默认为空）
    timeout: 300ms # 连接超时时间（毫秒）

  rabbitmq:
    host: rabbit
    port: 5672
    virtual-host: /mall
    username: mall
    password: mall

mongo:
  insert:
    sqlEnable: true # 用于控制是否通过数据库数据来插入mongo

logging:
  file:
    path: /var/logs
  level:
    root: info
    com.macro.mall: info

logstash:
  host: logstash

alipay:
  gatewayUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
  appId: your appId
  alipayPublicKey: your alipayPublicKey
  appPrivateKey: your appPrivateKey
  returnUrl: http://*************:8060/#/pages/money/paySuccess
  notifyUrl:

