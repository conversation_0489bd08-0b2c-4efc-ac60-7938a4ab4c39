package com.macro.mall.portal.component;

import com.macro.mall.portal.domain.CancelOrderMessage;
import com.macro.mall.portal.service.OmsPortalOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 取消订单消息的接收者
 * Created by macro on 2018/9/14.
 */
@Component
@RabbitListener(queues = "mall.order.cancel")
public class CancelOrderReceiver {
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelOrderReceiver.class);

    @Autowired
    private OmsPortalOrderService portalOrderService;

    @RabbitHandler
    public void handle(CancelOrderMessage message) {
        if (message == null || message.getOrderSn() == null || message.getOrderChannelCode() == null) {
            LOGGER.warn("收到空的取消订单消息或字段缺失，忽略处理");
            return;
        }

        try {
            portalOrderService.cancelOrder(message.getOrderSn(), message.getOrderChannelCode());
            LOGGER.info("取消订单成功：orderSn={}, channel={}", message.getOrderSn(), message.getOrderChannelCode());
        } catch (Exception e) {
            LOGGER.error("取消订单失败：orderSn={}, channel={}, 原因：{}",
                    message.getOrderSn(), message.getOrderChannelCode(), e.getMessage(), e);
        }
    }
}
