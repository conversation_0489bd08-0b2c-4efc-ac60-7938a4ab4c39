<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsAdminLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsAdminLoginLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="BIGINT" property="adminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, admin_id, create_time, ip, address, user_agent
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsAdminLoginLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_admin_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_admin_login_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_admin_login_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsAdminLoginLogExample">
    delete from ums_admin_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsAdminLoginLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_admin_login_log (admin_id, create_time, ip, 
      address, user_agent)
    values (#{adminId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{ip,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{userAgent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsAdminLoginLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_admin_login_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="userAgent != null">
        user_agent,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        #{userAgent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsAdminLoginLogExample" resultType="java.lang.Long">
    select count(*) from ums_admin_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_admin_login_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adminId != null">
        admin_id = #{record.adminId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.userAgent != null">
        user_agent = #{record.userAgent,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_admin_login_log
    set id = #{record.id,jdbcType=BIGINT},
      admin_id = #{record.adminId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      ip = #{record.ip,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      user_agent = #{record.userAgent,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsAdminLoginLog">
    update ums_admin_login_log
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="userAgent != null">
        user_agent = #{userAgent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsAdminLoginLog">
    update ums_admin_login_log
    set admin_id = #{adminId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      ip = #{ip,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>