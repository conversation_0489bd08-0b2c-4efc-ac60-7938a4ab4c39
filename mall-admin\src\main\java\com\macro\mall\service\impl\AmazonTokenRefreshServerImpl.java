package com.macro.mall.service.impl;

import com.macro.mall.mapper.UmsMemberMapper;
import com.macro.mall.model.UmsMember;
import com.macro.mall.service.AmazonTokenRefreshServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Date;
import java.util.Map;

@Service
public class AmazonTokenRefreshServerImpl implements AmazonTokenRefreshServer {

    @Value("${amazon.client_id}")
    private String clientId;

    @Value("${amazon.client_secret}")
    private String clientSecret;

    @Autowired
    private UmsMemberMapper umsMemberMapper;

    private RestTemplate restTemplate = new RestTemplate();

    @Override
    public String getValidAccessToken(UmsMember member) {
        // 预留2分钟作为缓冲时间
        if (member.getAmazonTokenExpireTime() != null &&
                member.getAmazonTokenExpireTime().after(new Date(System.currentTimeMillis() + 2 * 60 * 1000))) {
            return member.getAmazonAccessToken();
        }

        // 需要刷新token
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "refresh_token");
        params.add("refresh_token", member.getAmazonRefreshToken());
        params.add("client_id", clientId);
        params.add("client_secret", clientSecret);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(
                "https://api.amazon.com/auth/o2/token", request, Map.class);

        Map<String, Object> body = response.getBody();
        if (body == null || body.get("access_token") == null) {
            throw new RuntimeException("刷新access_token失败");
        }

        String accessToken = (String) body.get("access_token");
        Integer expiresIn = (Integer) body.get("expires_in");
        Date expireTime = Date.from(Instant.now().plusSeconds(expiresIn));

        // 更新数据库
        member.setAmazonAccessToken(accessToken);
        member.setAmazonTokenExpireTime(expireTime);
        umsMemberMapper.updateByPrimaryKey(member);

        return accessToken;
    }
}

