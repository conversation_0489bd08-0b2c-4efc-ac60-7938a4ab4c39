<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SheinAuthMapper">
    <resultMap id="BaseResultMap" type="com.macro.mall.model.SheinAuthEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="open_key_id" property="openKeyId"/>
        <result column="secret_key" property="secretKey"/>
        <result column="supplier_source" property="supplierSource"/>
        <result column="language" property="language"/>
        <result column="valid" property="valid"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>



    <!-- 字段列表复用 -->
    <sql id="Base_Column_List">
        id, user_id, supplier_id, open_key_id, secret_key,
        supplier_source, language, valid, create_time, update_time
    </sql>

    <!-- 插入或更新 -->
    <insert id="insertOrUpdate" parameterType="com.macro.mall.model.SheinAuthEntity">
        INSERT INTO shein_auth (
            user_id, supplier_id, open_key_id, secret_key,
            supplier_source, language, valid, create_time, update_time
        ) VALUES (
                     #{userId}, #{supplierId}, #{openKeyId}, #{secretKey},
                     #{supplierSource}, #{language}, #{valid}, #{createTime}, #{updateTime}
                 )
        ON DUPLICATE KEY UPDATE
                             open_key_id = #{openKeyId},
                             secret_key = #{secretKey},
                             supplier_source = #{supplierSource},
                             language = #{language},
                             valid = #{valid},
                             update_time = #{updateTime}
    </insert>

    <!-- 根据 userId 查询 -->
    <select id="selectByUserId" resultType="com.macro.mall.model.SheinAuthEntity">
        SELECT <include refid="Base_Column_List" />
        FROM shein_auth
        WHERE user_id = #{userId}
        AND valid = 1
        LIMIT 1
    </select>
    <select id="selectSupplierIdByUserId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT supplier_id
        FROM shein_auth
        WHERE user_id = #{userId}
        AND valid = 1
    </select>
</mapper>