package com.macro.mall.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dto.PendingPublishDTO;
import com.macro.mall.model.PendingPublishEntity;
import com.macro.mall.model.PlatformEnum;
import com.macro.mall.service.PendingPublishService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/pendingpublish")
public class PendingPublishController {

    @Autowired
    private PendingPublishService pendingPublishService;

    @GetMapping("/platforms")
    @ApiOperation(value = "查询平台列表")
    public CommonResult<List<Map<Long, String>>> getPlatforms() {
        List<Map<Long, String>> platforms = pendingPublishService.getPlatformList();
        return CommonResult.success(platforms);
    }

    @GetMapping("/shops")
    @ApiOperation(value = "查询店铺列表")
    public CommonResult<List<Integer>> getShopsByPlatform(@RequestParam Long platformId) {
        List<Integer> supplierIds = pendingPublishService.getShopsByPlatform(platformId);
        return CommonResult.success(supplierIds);
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加购物车商品")
    public CommonResult addShoppingCart(@RequestBody PendingPublishDTO pendingPublishDTO) {
        pendingPublishService.addPendingPublish(pendingPublishDTO);
        return CommonResult.success(null);
    }

    @GetMapping("/list")
    @ApiOperation(value = "查看购物车列表")
    public CommonResult<List<PendingPublishEntity>> list() {
        return CommonResult.success(pendingPublishService.showPendingPublishList());
    }

    @DeleteMapping("/clean/{productId}")
    @ApiOperation(value = "取消商品选择")
    public CommonResult cleanByProductId(@PathVariable Long productId) {

        boolean success = pendingPublishService.cleanPendingPublishByProductId(productId);
        return success ? CommonResult.success(null) : CommonResult.failed("取消失败，记录不存在");
    }

    @DeleteMapping("/clean")
    @ApiOperation(value = "清空用户购物车")
    public CommonResult clean() {
        pendingPublishService.cleanPendingPublish();
        return CommonResult.success(null);
    }


}
