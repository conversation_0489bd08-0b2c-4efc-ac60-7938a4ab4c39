<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsIntegrationChangeHistoryMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsIntegrationChangeHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="change_type" jdbcType="INTEGER" property="changeType" />
    <result column="change_count" jdbcType="INTEGER" property="changeCount" />
    <result column="operate_man" jdbcType="VARCHAR" property="operateMan" />
    <result column="operate_note" jdbcType="VARCHAR" property="operateNote" />
    <result column="source_type" jdbcType="INTEGER" property="sourceType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_id, create_time, change_type, change_count, operate_man, operate_note, 
    source_type
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsIntegrationChangeHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_integration_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_integration_change_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_integration_change_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsIntegrationChangeHistoryExample">
    delete from ums_integration_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsIntegrationChangeHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_integration_change_history (member_id, create_time, change_type, 
      change_count, operate_man, operate_note, 
      source_type)
    values (#{memberId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{changeType,jdbcType=INTEGER}, 
      #{changeCount,jdbcType=INTEGER}, #{operateMan,jdbcType=VARCHAR}, #{operateNote,jdbcType=VARCHAR}, 
      #{sourceType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsIntegrationChangeHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_integration_change_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        member_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="changeType != null">
        change_type,
      </if>
      <if test="changeCount != null">
        change_count,
      </if>
      <if test="operateMan != null">
        operate_man,
      </if>
      <if test="operateNote != null">
        operate_note,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeCount != null">
        #{changeCount,jdbcType=INTEGER},
      </if>
      <if test="operateMan != null">
        #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="operateNote != null">
        #{operateNote,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsIntegrationChangeHistoryExample" resultType="java.lang.Long">
    select count(*) from ums_integration_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_integration_change_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.changeType != null">
        change_type = #{record.changeType,jdbcType=INTEGER},
      </if>
      <if test="record.changeCount != null">
        change_count = #{record.changeCount,jdbcType=INTEGER},
      </if>
      <if test="record.operateMan != null">
        operate_man = #{record.operateMan,jdbcType=VARCHAR},
      </if>
      <if test="record.operateNote != null">
        operate_note = #{record.operateNote,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_integration_change_history
    set id = #{record.id,jdbcType=BIGINT},
      member_id = #{record.memberId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      change_type = #{record.changeType,jdbcType=INTEGER},
      change_count = #{record.changeCount,jdbcType=INTEGER},
      operate_man = #{record.operateMan,jdbcType=VARCHAR},
      operate_note = #{record.operateNote,jdbcType=VARCHAR},
      source_type = #{record.sourceType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsIntegrationChangeHistory">
    update ums_integration_change_history
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeType != null">
        change_type = #{changeType,jdbcType=INTEGER},
      </if>
      <if test="changeCount != null">
        change_count = #{changeCount,jdbcType=INTEGER},
      </if>
      <if test="operateMan != null">
        operate_man = #{operateMan,jdbcType=VARCHAR},
      </if>
      <if test="operateNote != null">
        operate_note = #{operateNote,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsIntegrationChangeHistory">
    update ums_integration_change_history
    set member_id = #{memberId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      change_type = #{changeType,jdbcType=INTEGER},
      change_count = #{changeCount,jdbcType=INTEGER},
      operate_man = #{operateMan,jdbcType=VARCHAR},
      operate_note = #{operateNote,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>