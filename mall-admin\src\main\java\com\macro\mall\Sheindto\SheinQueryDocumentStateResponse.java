package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SHEIN查询商品审核状态响应DTO
 */
@Data
public class SheinQueryDocumentStateResponse {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 审核状态信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    @JsonProperty("traceId")
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 数据列表
         */
        private List<DataItem> data;
        
        /**
         * 元数据
         */
        private Meta meta;
    }
    
    @Data
    public static class DataItem {
        /**
         * SPU名称
         */
        @JsonProperty("spuName")
        private String spuName;
        
        /**
         * 版本号
         */
        private String version;
        
        /**
         * SKC列表
         */
        @JsonProperty("skcList")
        private List<SkcItem> skcList;
    }
    
    @Data
    public static class SkcItem {
        /**
         * SKC名称
         */
        @JsonProperty("skcName")
        private String skcName;
        
        /**
         * 公文号
         */
        @JsonProperty("documentSn")
        private String documentSn;
        
        /**
         * 公文状态：-1：表单失效 1：(待审核) 2：(审批成功) 3：(审批失败) 4：(已撤销) 5：申请中
         */
        @JsonProperty("documentState")
        private Integer documentState;
        
        /**
         * 审批失败的原因
         */
        @JsonProperty("failedReason")
        private List<FailedReason> failedReason;
    }
    
    @Data
    public static class FailedReason {
        /**
         * 语种
         */
        private String language;
        
        /**
         * 内容
         */
        private String content;
    }
    
    @Data
    public static class Meta {
        /**
         * 数据总数量
         */
        private Integer count;
        
        /**
         * 用户自定义扩展
         */
        @JsonProperty("customObj")
        private Object customObj;
    }
}
