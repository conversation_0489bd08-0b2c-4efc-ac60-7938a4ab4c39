package com.macro.mall.dao;

import com.macro.mall.Sheindto.SheinProductConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SHEIN商品配置自定义Dao
 */
public interface SheinProductConfigDao {

    /**
     * 插入商品配置
     * @param config 配置信息
     * @return 影响行数
     */
    int insert(SheinProductConfig config);

    /**
     * 根据商品ID查询配置
     * @param productId 商品ID
     * @return 商品配置
     */
    SheinProductConfig selectByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID删除配置
     * @param productId 商品ID
     * @return 影响行数
     */
    int deleteByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID更新配置
     * @param config 配置信息
     * @return 影响行数
     */
    int updateByProductId(SheinProductConfig config);

    /**
     * 查询所有配置列表
     * @return 配置列表
     */
    List<SheinProductConfig> selectAllConfigs();

    /**
     * 分页查询配置列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 配置列表
     */
    List<SheinProductConfig> selectConfigsWithPaging(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 查询配置总数
     * @return 总数
     */
    Long countConfigs();
}
