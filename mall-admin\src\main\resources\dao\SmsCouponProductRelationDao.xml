<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.SmsCouponProductRelationDao">
    <insert id="insertList">
        INSERT INTO sms_coupon_product_relation (product_id,product_name,product_sn,coupon_id) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.productId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR},
            #{item.productSn,jdbcType=VARCHAR},
            #{item.couponId,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>