package com.macro.mall.portal.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.portal.config.AlipayConfig;
import com.macro.mall.portal.domain.AliPayParam;
import com.macro.mall.portal.service.AlipayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @auther macrozheng
 * @description 支付宝支付Controller
 * @date 2023/9/8
 * @github https://github.com/macrozheng
 */
@Controller
@Api(tags = "AlipayController")
@Tag(name = "AlipayController", description = "支付宝支付相关接口")
@RequestMapping("/alipay")
public class AlipayController {

//    @Autowired
//    private AlipayConfig alipayConfig;
//    @Autowired
//    private AlipayService alipayService;
//
//    @ApiOperation("支付宝电脑网站支付")
//    @RequestMapping(value = "/pay", method = RequestMethod.GET)
//    public void pay(AliPayParam aliPayParam, HttpServletResponse response) throws IOException {
//        response.setContentType("text/html;charset=" + alipayConfig.getCharset());
//        response.getWriter().write(alipayService.pay(aliPayParam));
//        response.getWriter().flush();
//        response.getWriter().close();
//    }
//
//    @ApiOperation("支付宝手机网站支付")
//    @RequestMapping(value = "/webPay", method = RequestMethod.GET)
//    public void webPay(AliPayParam aliPayParam, HttpServletResponse response) throws IOException {
//        response.setContentType("text/html;charset=" + alipayConfig.getCharset());
//        response.getWriter().write(alipayService.webPay(aliPayParam));
//        response.getWriter().flush();
//        response.getWriter().close();
//    }
//
//    @ApiOperation(value = "支付宝异步回调",notes = "必须为POST请求，执行成功返回success，执行失败返回failure")
//    @RequestMapping(value = "/notify", method = RequestMethod.POST)
//    public String notify(HttpServletRequest request){
//        Map<String, String> params = new HashMap<>();
//        Map<String, String[]> requestParams = request.getParameterMap();
//        for (String name : requestParams.keySet()) {
//            params.put(name, request.getParameter(name));
//        }
//        return alipayService.notify(params);
//    }
//
//    @ApiOperation(value = "支付宝统一收单线下交易查询",notes = "订单支付成功返回交易状态：TRADE_SUCCESS")
//    @RequestMapping(value = "/query", method = RequestMethod.GET)
//    @ResponseBody
//    public CommonResult<String> query(String outTradeNo, String tradeNo){
//        return CommonResult.success(alipayService.query(outTradeNo,tradeNo));
//    }
}
