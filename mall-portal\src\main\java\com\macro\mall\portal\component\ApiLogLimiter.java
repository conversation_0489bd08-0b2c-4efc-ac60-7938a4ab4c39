package com.macro.mall.portal.component;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class ApiLogLimiter {
    private final Map<String, AtomicInteger> logCountMap = new ConcurrentHashMap<>();
    private final Map<String, Long> timestampMap = new ConcurrentHashMap<>();

    private static final int MAX_LOGS_PER_MINUTE = 300;

    public synchronized boolean allow(String key) {
        long currentMinute = System.currentTimeMillis() / 60000;

        Long lastMinute = timestampMap.get(key);
        AtomicInteger count = logCountMap.get(key);

        if (lastMinute == null || !lastMinute.equals(currentMinute)) {
            timestampMap.put(key, currentMinute);
            count = new AtomicInteger(0);
            logCountMap.put(key, count);
        }

        int currentCount = count.get();
        if (currentCount >= MAX_LOGS_PER_MINUTE) {
            return false;
        }

        count.incrementAndGet();
        return true;
    }
}