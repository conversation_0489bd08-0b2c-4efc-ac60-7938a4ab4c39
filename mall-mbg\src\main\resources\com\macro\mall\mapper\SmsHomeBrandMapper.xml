<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.SmsHomeBrandMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.SmsHomeBrand">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="recommend_status" jdbcType="INTEGER" property="recommendStatus" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, brand_id, brand_name, recommend_status, sort
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.SmsHomeBrandExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sms_home_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sms_home_brand
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sms_home_brand
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.SmsHomeBrandExample">
    delete from sms_home_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.SmsHomeBrand">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_home_brand (brand_id, brand_name, recommend_status, 
      sort)
    values (#{brandId,jdbcType=BIGINT}, #{brandName,jdbcType=VARCHAR}, #{recommendStatus,jdbcType=INTEGER}, 
      #{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.SmsHomeBrand">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sms_home_brand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="recommendStatus != null">
        recommend_status,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="recommendStatus != null">
        #{recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.SmsHomeBrandExample" resultType="java.lang.Long">
    select count(*) from sms_home_brand
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sms_home_brand
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=BIGINT},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendStatus != null">
        recommend_status = #{record.recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sms_home_brand
    set id = #{record.id,jdbcType=BIGINT},
      brand_id = #{record.brandId,jdbcType=BIGINT},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      recommend_status = #{record.recommendStatus,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.SmsHomeBrand">
    update sms_home_brand
    <set>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="recommendStatus != null">
        recommend_status = #{recommendStatus,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.SmsHomeBrand">
    update sms_home_brand
    set brand_id = #{brandId,jdbcType=BIGINT},
      brand_name = #{brandName,jdbcType=VARCHAR},
      recommend_status = #{recommendStatus,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>