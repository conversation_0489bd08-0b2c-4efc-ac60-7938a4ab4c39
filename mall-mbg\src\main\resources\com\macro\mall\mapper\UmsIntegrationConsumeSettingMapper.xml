<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsIntegrationConsumeSettingMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsIntegrationConsumeSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deduction_per_amount" jdbcType="INTEGER" property="deductionPerAmount" />
    <result column="max_percent_per_order" jdbcType="INTEGER" property="maxPercentPerOrder" />
    <result column="use_unit" jdbcType="INTEGER" property="useUnit" />
    <result column="coupon_status" jdbcType="INTEGER" property="couponStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, deduction_per_amount, max_percent_per_order, use_unit, coupon_status
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsIntegrationConsumeSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_integration_consume_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_integration_consume_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_integration_consume_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsIntegrationConsumeSettingExample">
    delete from ums_integration_consume_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsIntegrationConsumeSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_integration_consume_setting (deduction_per_amount, max_percent_per_order, 
      use_unit, coupon_status)
    values (#{deductionPerAmount,jdbcType=INTEGER}, #{maxPercentPerOrder,jdbcType=INTEGER}, 
      #{useUnit,jdbcType=INTEGER}, #{couponStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsIntegrationConsumeSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_integration_consume_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deductionPerAmount != null">
        deduction_per_amount,
      </if>
      <if test="maxPercentPerOrder != null">
        max_percent_per_order,
      </if>
      <if test="useUnit != null">
        use_unit,
      </if>
      <if test="couponStatus != null">
        coupon_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deductionPerAmount != null">
        #{deductionPerAmount,jdbcType=INTEGER},
      </if>
      <if test="maxPercentPerOrder != null">
        #{maxPercentPerOrder,jdbcType=INTEGER},
      </if>
      <if test="useUnit != null">
        #{useUnit,jdbcType=INTEGER},
      </if>
      <if test="couponStatus != null">
        #{couponStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsIntegrationConsumeSettingExample" resultType="java.lang.Long">
    select count(*) from ums_integration_consume_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_integration_consume_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deductionPerAmount != null">
        deduction_per_amount = #{record.deductionPerAmount,jdbcType=INTEGER},
      </if>
      <if test="record.maxPercentPerOrder != null">
        max_percent_per_order = #{record.maxPercentPerOrder,jdbcType=INTEGER},
      </if>
      <if test="record.useUnit != null">
        use_unit = #{record.useUnit,jdbcType=INTEGER},
      </if>
      <if test="record.couponStatus != null">
        coupon_status = #{record.couponStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_integration_consume_setting
    set id = #{record.id,jdbcType=BIGINT},
      deduction_per_amount = #{record.deductionPerAmount,jdbcType=INTEGER},
      max_percent_per_order = #{record.maxPercentPerOrder,jdbcType=INTEGER},
      use_unit = #{record.useUnit,jdbcType=INTEGER},
      coupon_status = #{record.couponStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsIntegrationConsumeSetting">
    update ums_integration_consume_setting
    <set>
      <if test="deductionPerAmount != null">
        deduction_per_amount = #{deductionPerAmount,jdbcType=INTEGER},
      </if>
      <if test="maxPercentPerOrder != null">
        max_percent_per_order = #{maxPercentPerOrder,jdbcType=INTEGER},
      </if>
      <if test="useUnit != null">
        use_unit = #{useUnit,jdbcType=INTEGER},
      </if>
      <if test="couponStatus != null">
        coupon_status = #{couponStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsIntegrationConsumeSetting">
    update ums_integration_consume_setting
    set deduction_per_amount = #{deductionPerAmount,jdbcType=INTEGER},
      max_percent_per_order = #{maxPercentPerOrder,jdbcType=INTEGER},
      use_unit = #{useUnit,jdbcType=INTEGER},
      coupon_status = #{couponStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>