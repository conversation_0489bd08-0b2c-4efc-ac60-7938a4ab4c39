package com.macro.mall.portal.domain;

import io.swagger.annotations.ApiModelProperty;

public class OrderActionRequest {
    private String username;
    private String password;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    private String orderChannelCode;

    // -------- Get<PERSON> & Setters --------
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getOrderSn() {
        return orderSn;
    }
    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }
    public String getOrderChannelCode() { return orderChannelCode; }
    public void setOrderChannelCode(String orderChannelCode) { this.orderChannelCode = orderChannelCode; }
}
