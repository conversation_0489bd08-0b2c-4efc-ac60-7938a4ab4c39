package com.macro.mall.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.macro.mall.dto.*;
import com.macro.mall.model.CusBaseLogistics;
import com.macro.mall.model.CusLogistics;
import com.macro.mall.model.CusLogisticsHistory;
import com.macro.mall.service.AdminLogService;
import com.macro.mall.service.CusLogisticsService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.fasterxml.jackson.core.type.TypeReference;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "CusUserController")
@Tag(name = "CusUserController", description = "清关物流信息管理接口")
@RequestMapping("/cus")
@RequiredArgsConstructor
public class CusUserController {
    private static final Logger log = LoggerFactory.getLogger(CusUserController.class);
    private final CusLogisticsService cusLogisticsService;
    @Autowired
    private AdminLogService adminLogService;

    /**
     * 新增或更新物流信息
     */
    @PostMapping("/saveOrUpdate")
    public ResponseEntity<String> saveOrUpdateLogistics(HttpServletRequest request,
                                                        @RequestBody List<CusLogistics> logisticsList) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "保存或更新物流记录";
        Date now = new Date();

        // 参数转 JSON
        String requestParams = JsonUtil.toJson(logisticsList);

        try {
            for (CusLogistics logistics : logisticsList) {
                cusLogisticsService.saveOrUpdateLogistics(logistics);
            }

            // 记录成功日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok("操作成功");
        } catch (Exception e) {
            // 记录异常日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);

            log.error("保存或更新物流记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("操作失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有物流信息（带过滤条件）
     */
    @PostMapping("/fetchAll")
    public ResponseEntity<List<CusBaseLogistics>> fetchAllLogistics(HttpServletRequest request,
                                                                    @RequestBody CusQueryParam queryParam) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "获取所有物流信息";
        Date now = new Date();

        // 参数转成 JSON
        String requestParams = JsonUtil.toJson(queryParam);

        try {
            List<CusBaseLogistics> logisticsList = cusLogisticsService.getAllLogistics(queryParam);

            // 记录操作日志（成功）
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok(logisticsList);
        } catch (Exception e) {
            // 记录操作日志（异常）
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);

            log.error("获取物流信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.emptyList());
        }
    }

    /**
     * 获取指定物流详细信息
     */
    @PostMapping("/query")
    public ResponseEntity<CusLogistics> queryLogistics(HttpServletRequest request, @RequestBody Integer id) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "查询物流信息";
        Date now = new Date();

        String requestParams = id != null ? id.toString() : "null";

        try {
            log.info("Querying logistics with params: {}", id);
            CusLogistics result = cusLogisticsService.queryLogistics(id);

            if (result == null) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（未找到记录）", requestParams, ip, now);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }

            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            log.error("查询物流信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 批量删除物流记录
     */
    @PostMapping("/removeLogistics")
    public ResponseEntity<String> removeLogistics(HttpServletRequest request, @RequestBody List<Integer> ids) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "删除物流记录";
        Date now = new Date();

        String requestParams = JsonUtil.toJson(ids);

        if (ids == null || ids.isEmpty()) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（请求数据为空）", requestParams, ip, now);
            return ResponseEntity.badRequest().body("请求数据不能为空");
        }

        try {
            int totalDeleted = cusLogisticsService.deleteLogisticsByIds(ids);

            if (totalDeleted > 0) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);
                return ResponseEntity.ok(totalDeleted + " 条物流记录已删除");
            } else {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（未找到匹配记录）", requestParams, ip, now);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("未找到匹配的物流记录，删除失败");
            }
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            log.error("删除物流记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新物流状态
     */
    @PostMapping("/updateStatusByIds")
    public ResponseEntity<String> updateStatusByIds(HttpServletRequest request, @RequestBody List<Integer> ids) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "批量更新物流状态为关闭";
        Date now = new Date();

        String requestParams = JsonUtil.toJson(ids);

        if (ids == null || ids.isEmpty()) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（ID列表为空）", requestParams, ip, now);
            return ResponseEntity.badRequest().body("ID 列表不能为空");
        }

        try {
            String targetStatus = "close";
            int updatedCount = cusLogisticsService.updateStatusByIds(ids, targetStatus);

            if (updatedCount > 0) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);
                return ResponseEntity.ok("成功更新 " + updatedCount + " 条记录");
            } else {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（未找到匹配记录）", requestParams, ip, now);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("未找到匹配记录，更新失败");
            }
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            log.error("批量更新状态失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("状态更新失败: " + e.getMessage());
        }
    }


    /**
     * 更新物流轨迹信息
     */
    @PostMapping("/updateLogisticsNote")
    public ResponseEntity<String> updateLogisticsNote(HttpServletRequest request,
                                                      @RequestBody CusUpdateNoteRequest updateRequest) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "更新物流备注";
        Date now = new Date();

        // 将参数序列化成 JSON 字符串
        String requestParams = JsonUtil.toJson(updateRequest);

        try {
            // 验证 note 是否为空
            if (updateRequest.getNote() == null || updateRequest.getNote().trim().isEmpty()) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（note为空）", requestParams, ip, now);
                return ResponseEntity.badRequest().body("note 不能为空");
            }

            // 验证 ids 是否为空
            if (updateRequest.getIds() == null || updateRequest.getIds().isEmpty()) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（ids为空）", requestParams, ip, now);
                return ResponseEntity.badRequest().body("ids 不能为空");
            }

            // 执行更新
            int updatedCount = 0;
            for (Integer id : updateRequest.getIds()) {
                boolean updated = cusLogisticsService.updateLogisticsNote(id, updateRequest.getNote());
                if (updated) updatedCount++;
            }

            if (updatedCount > 0) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);
                return ResponseEntity.ok(updatedCount + " 条物流记录已更新");
            } else {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（无匹配记录）", requestParams, ip, now);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("未找到匹配的物流记录，更新失败");
            }

        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            log.error("更新物流记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("更新失败: " + e.getMessage());
        }
    }




    /**
     * 上传清关结果文件
     */
    @PostMapping("/uploadClearanceResult")
    public ResponseEntity<?> uploadClearanceResult(HttpServletRequest request,
                                                   @RequestParam("file") MultipartFile file,
                                                   @RequestParam("requestData") String requestDataJson) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "上传报关结果";
        Date now = new Date();

        ObjectMapper objectMapper = new ObjectMapper();
        CusUploadClearanceRequest clearanceRequest;

        try {
            clearanceRequest = objectMapper.readValue(requestDataJson, CusUploadClearanceRequest.class);
        } catch (IOException e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（请求参数 JSON 格式错误）",
                    String.format("{\"requestData\": \"%s\"}", requestDataJson), ip, now);
            return ResponseEntity.badRequest().body("Invalid JSON format");
        }

        String requestParams = String.format("{\"fileName\": \"%s\", \"requestData\": %s}",
                file.getOriginalFilename(),
                JsonUtil.toJson(clearanceRequest));

        try {
            List<String> result = cusLogisticsService.uploadFileAndUpdate(
                    file,
                    clearanceRequest.getIds(),
                    clearanceRequest.getContainerNumber(),
                    "customs_clearance_result",
                    "result/"
            );

            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            log.error("上传报关结果失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传清关材料文件
     */
    @PostMapping("/uploadClearanceMaterials")
    public ResponseEntity<?> uploadClearanceMaterials(HttpServletRequest request,
                                                      @RequestParam("file") MultipartFile file,
                                                      @RequestParam("requestData") String requestDataJson) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "上传报关材料";
        Date now = new Date();

        ObjectMapper objectMapper = new ObjectMapper();
        CusUploadClearanceRequest clearanceRequest;

        try {
            clearanceRequest = objectMapper.readValue(requestDataJson, CusUploadClearanceRequest.class);
        } catch (IOException e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（请求参数 JSON 格式错误）",
                    String.format("{\"requestData\": \"%s\"}", requestDataJson), ip, now);

            return ResponseEntity.badRequest().body("Invalid JSON format");
        }

        // 记录请求参数（包含文件名和解析后的请求对象JSON）
        String requestParams = String.format("{\"fileName\": \"%s\", \"requestData\": %s}",
                file.getOriginalFilename(),
                JsonUtil.toJson(clearanceRequest));

        try {
            List<String> result = cusLogisticsService.uploadFileAndUpdate(
                    file,
                    clearanceRequest.getIds(),
                    clearanceRequest.getContainerNumber(),
                    "customs_clearance_materials",
                    "materials/"
            );

            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);

            log.error("上传报关材料失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    @PostMapping("/uploadfile")
    public ResponseEntity<?> uploadfile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "POST";
        String operation = "上传文件";
        Date now = new Date();

        // 记录文件名作为请求参数
        String requestParams = String.format("{\"fileName\": \"%s\"}", file.getOriginalFilename());

        try {
            List<String> result = cusLogisticsService.uploadFile(file);

            // 记录成功日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            // 记录异常日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);

            log.error("文件上传失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("上传失败: " + e.getMessage());
        }
    }


    /**
     * 下载文件
     */
    @GetMapping("/download/{folder}/{fileName}")
    public ResponseEntity<Resource> downloadFile(HttpServletRequest request,
                                                 @PathVariable String folder,
                                                 @PathVariable String fileName) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "GET";
        String operation = "下载文件";
        Date now = new Date();

        // 构建请求参数字符串
        String requestParams = String.format("{\"folder\": \"%s\", \"fileName\": \"%s\"}", folder, fileName);

        try {
            Path rootPath = Paths.get("/home/<USER>/server/download").toAbsolutePath().normalize();
            Path targetPath = rootPath.resolve(folder).resolve(fileName).normalize();

            if (!targetPath.startsWith(rootPath)) {
                // 记录日志：非法路径访问
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（非法路径）", requestParams, ip, now);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            Resource resource = new UrlResource(targetPath.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                // 记录日志：文件不存在或不可读
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（文件不存在）", requestParams, ip, now);
                return ResponseEntity.notFound().build();
            }

            String contentType = Files.probeContentType(targetPath);
            if (contentType == null) {
                contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
            }

            // 记录日志：成功下载
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .body(resource);

        } catch (IOException e) {
            // 记录日志：异常情况
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    /**
     * 查询历史轨迹
     */
    @GetMapping("/getLogisticsHistory")
    public ResponseEntity<?> getLogisticsHistory(HttpServletRequest request,
                                                 @RequestParam Long logisticsId) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "GET";
        String operation = "查询物流历史";
        Date now = new Date();

        // 构建参数 JSON
        String requestParams = String.format("{\"logisticsId\": %d}", logisticsId);

        try {
            List<CusLogisticsHistory> historyList = cusLogisticsService.getLogisticsHistoryByLogisticsId(logisticsId);

            // 记录成功日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);

            return ResponseEntity.ok(historyList);
        } catch (Exception e) {
            // 记录失败日志
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);

            log.error("查询历史轨迹失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("查询失败: " + e.getMessage());
        }
    }

}