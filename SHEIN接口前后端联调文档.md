# SHEIN商品发布准备阶段接口前后端联调文档

## 概述

本文档描述了SHEIN商品发布准备阶段相关接口的前后端联调规范。由于SHEIN平台接口暂时不可用，后端已采用硬编码方式返回模拟数据，确保前端开发不受影响。

## 基础信息

- **基础路径**: `/shein/product`
- **数据格式**: JSON

## 接口列表

### 1. 获取商品发布规范

**接口地址**: `POST /shein/product/publish-spec`

**请求参数** (可选):
```json
{
  "categoryId": 1003,
  "spuName": "Summer Dress"
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "defaultLanguage": "en",
    "currency": "USD",
    "supportSaleAttributeSort": true,
    "fillInStandardList": [
      {
        "module": "basic_info",
        "fieldKey": "product_name",
        "required": true,
        "show": true
      },
      {
        "module": "basic_info", 
        "fieldKey": "product_description",
        "required": true,
        "show": true
      }
    ],
    "pictureConfigList": [
      {
        "pictureType": "main_image",
        "minCount": 1,
        "maxCount": 10,
        "requirement": "主图要求：尺寸不小于800x800像素"
      }
    ]
  }
}
```

### 2. 获取可用品牌列表

**接口地址**: `POST /shein/product/brands`

**请求参数**: 无

**响应示例**:
```json
{
  "code": "0",
  "msg": "success", 
  "bbl": "test_bbl",
  "info": {
    "data": [
      {
        "brandCode": "SHEIN",
        "brandName": "SHEIN"
      },
      {
        "brandCode": "ROMWE",
        "brandName": "ROMWE"
      },
      {
        "brandCode": "DAZY",
        "brandName": "DAZY"
      }
    ],
    "meta": {
      "count": 3,
      "customObj": null
    }
  }
}
```

### 3. 获取类目树

**接口地址**: `POST /shein/product/categories`

**请求参数**: 无

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl", 
  "info": {
    "data": [
      {
        "categoryId": 1001,
        "productTypeId": 2001,
        "parentCategoryId": 0,
        "categoryName": "服装",
        "lastCategory": false,
        "children": [
          {
            "categoryId": 1002,
            "productTypeId": 2002,
            "parentCategoryId": 1001,
            "categoryName": "女装",
            "lastCategory": false,
            "children": [
              {
                "categoryId": 1003,
                "productTypeId": 2003,
                "parentCategoryId": 1002,
                "categoryName": "连衣裙",
                "lastCategory": true,
                "children": null
              }
            ]
          }
        ]
      }
    ],
    "meta": {
      "count": 1,
      "customObj": null
    }
  }
}
```

### 4. 查询店铺可选属性

**接口地址**: `POST /shein/product/attribute-template`

**请求参数**:
```json
{
  "productTypeIdList": [2003, 2004]
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "data": [
      {
        "productTypeId": 2003,
        "supplierId": 12345,
        "attributeInfos": [
          {
            "attributeId": 1001,
            "attributeName": "颜色",
            "attributeIsShow": 1,
            "attributeType": 1,
            "attributeLabel": 1,
            "attributeMode": 1,
            "attributeInputNum": 1,
            "attributeStatus": 1,
            "attributeRemarkList": [1, 2],
            "attributeValueInfoList": [
              {
                "attributeValueId": 10001,
                "attributeValue": "红色",
                "isShow": 1,
                "isCustomAttributeValue": false
              },
              {
                "attributeValueId": 10002,
                "attributeValue": "蓝色", 
                "isShow": 1,
                "isCustomAttributeValue": false
              }
            ]
          }
        ]
      }
    ],
    "meta": {
      "count": 1,
      "customObj": null
    }
  }
}
```

### 5. 查询是否支持自定义属性值

**接口地址**: `POST /shein/product/custom-attribute-permission`

**请求参数**:
```json
{
  "category_id_list": [1003, 1004]
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "data": [
      {
        "last_category_id": 1003,
        "attribute_id": 1001,
        "has_permission": 1
      },
      {
        "last_category_id": 1003,
        "attribute_id": 1002,
        "has_permission": 0
      }
    ],
    "meta": {
      "count": 2,
      "customObj": null
    }
  }
}
```

### 6. 添加自定义属性值

**接口地址**: `POST /shein/product/add-custom-attribute-value`

**请求参数**:
```json
{
  "attribute_id": 1001,
  "attribute_value": "自定义颜色",
  "category_id": 1003,
  "attribute_value_name_multis": [
    {
      "language": "en",
      "attribute_value_name_multi": "Custom Color"
    }
  ]
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "supplier_id": 12345,
    "supplier_source": 1,
    "category_id": 1003,
    "attribute_id": 1001,
    "attribute_value_id": 1703123456789,
    "attribute_value_name": "自定义颜色",
    "attribute_value_multi_arr": [
      {
        "language": "en",
        "attribute_value_name_multi": "Custom Color"
      }
    ]
  }
}
```

### 7. 本地图片上传

**接口地址**: `POST /shein/product/upload-pic`

**请求参数**: 
- `image_type` (form-data): 图片类型 (1:主图; 2:细节图; 5:方块图; 6:色块图; 7:详情图)
- `file` (form-data): 图片文件

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "image_url": "https://img.ltwebstatic.com/images3_pi/2024/01/15/mock_image_1703123456789.jpg",
    "width": 800,
    "height": 800,
    "size": 102400,
    "image_hex_type": "JPEG"
  }
}
```

### 8. 图片链接转换

**接口地址**: `POST /shein/product/transform-pic`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "image_type": 1,
  "original_url": "https://example.com/image.jpg"
}
```

**响应示例**:
```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "original": "https://example.com/image.jpg",
    "transformed": "https://img.ltwebstatic.com/images3_pi/2024/01/15/transformed_1703123456789.jpg"
  }
}
```

### 9. 查询商家仓库列表

**接口地址**: `POST /shein/product/warehouse-list`

**请求参数**: 空

**响应示例**:
```json
{
  "code": "0",
  "msg": "OK",
  "info": {
    "list": [
      {
        "warehouseCode": "PS0426919682",
        "warehouseName": "Girona Warehouse",
        "saleCountryList": [
          "FR",
          "ES",
          "IT",
          "NL",
          "PL"
        ],
        "createType": 3,
        "warehouseType": 1,
        "authServiceCode": "",
        "authServiceName": ""
      },
      {
        "warehouseCode": "PS1993127180",
        "warehouseName": "EU Warehouse",
        "saleCountryList": [
          "DE",
          "FR",
          "ES",
          "IT",
          "NL",
          "PL"
        ],
        "createType": 3,
        "warehouseType": 1,
        "authServiceCode": "",
        "authServiceName": ""
      },
      {
        "warehouseCode": "PS8428226538",
        "warehouseName": "英国",
        "saleCountryList": [
          "GB"
        ],
        "createType": 1,
        "warehouseType": 1,
        "authServiceCode": "",
        "authServiceName": ""
      }
    ]
  },
  "bbl": null,
  "traceId": "trace_1750831578335"
}
```

### 10. 查询店铺可售站点

**接口地址**: `POST /shein/product/site-list`

**请求参数**: `{}` (空对象)

**响应示例**:

```json
{
  "code": "0",
  "msg": "success",
  "bbl": "test_bbl",
  "traceId": "trace_1703123456789",
  "info": {
    "data": [
      {
        "main_site": "US",
        "main_site_name": "美国",
        "sub_site_list": [
          {
            "site_name": "美国",
            "site_abbr": "US",
            "site_status": 1,
            "store_type": 1
          }
        ]
      },
      {
        "main_site": "EU",
        "main_site_name": "欧洲",
        "sub_site_list": [
          {
            "site_name": "德国",
            "site_abbr": "DE", 
            "site_status": 1,
            "store_type": 1
          },
          {
            "site_name": "法国",
            "site_abbr": "FR",
            "site_status": 1,
            "store_type": 1
          }
        ]
      }
    ],
    "meta": {
      "count": 2,
      "customObj": null
    }
  }
}
```

