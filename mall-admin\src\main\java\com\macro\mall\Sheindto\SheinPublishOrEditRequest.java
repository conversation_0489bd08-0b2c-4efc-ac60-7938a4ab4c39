package com.macro.mall.Sheindto;

import lombok.Data;
import java.util.List;

@Data
public class SheinPublishOrEditRequest {

    /** 商品品牌CODE，发布新品否，编辑商品否 */
    private String brandCode;

    /** 商品末级类目ID，发布新品是，编辑商品是 */
    private Long categoryId;

    /** 商品类型ID，发布新品是，编辑商品是 */
    private Long productTypeId;

    /** 固定传 "openapi"，发布新品否，编辑商品否 */
    private String sourceSystem;

    /** SPU唯一标识，发布新品否，编辑商品是 */
    private String spuName;

    /** 是否套装（1是，0否），发布新品是，编辑商品是 */
    private String suitFlag;

    /** 卖家自定义货号，发布新品是，编辑商品是 */
    private String supplierCode;

    /** 是否使用新版图片方案，发布新品否，编辑商品否 */
    private Boolean isSpuPic;

    /** SPU商品图片，发布新品否，编辑商品否 */
    private List<ImageInfo> imageInfo;

    /** 商品描述多语言，发布新品是，编辑商品是 */
    private List<MultiLanguageText> multiLanguageDescList;

    /** 商品名称多语言，发布新品是，编辑商品是 */
    private List<MultiLanguageText> multiLanguageNameList;

    private List<ProductAttributeList> productAttributeList;

    private List<SiteList> siteList;

    private List<SkcList> skcList;

    @Data
    public static class ImageInfo {
        /** 图片组编码，发布新品否，编辑商品是 */
        private String imageGroupCode;

        /** 图片列表，发布新品否，编辑商品是 */
        private List<ImageInfoListItem> imageInfoList;
    }

    @Data
    public static class ImageInfoListItem {
        /** 图片唯一ID，发布新品否，编辑商品是 */
        private Long imageItemId;

        /** 图片排序，发布新品是，编辑商品是 */
        private Integer imageSort;

        /** 图片类型，发布新品是，编辑商品是 */
        private Integer imageType;

        /** 图片链接，发布新品否，编辑商品否 */
        private String imageUrl;
    }

    @Data
    public static class MultiLanguageText {
        /** 语种缩写，发布新品是，编辑商品是 */
        private String language;

        /** 名称或描述文本，发布新品是，编辑商品是 */
        private String name;
    }

    @Data
    public static class ProductAttributeList {
        private Long attributeId;
    }

    @Data
    public static class SiteList {
        private String mainSite;

        private List<String> subSiteList;
    }

    @Data
    public static class SkcList {
        private List<ImageInfo> imageInfo;

        private List<SaleAttribute> saleAttribute;

        private String supplierCode;

        private String skcTitle;

        private List<SkuList> skuList;

        private String shelfRequire;

        private String shelfWay;
    }

    @Data
    public static class SaleAttribute {
        private Long attributeId;
    }

    @Data
    public static class SkuList {
        private CostInfo costInfo;

        private String height;

        private String length;

        private String width;

        private String weight;

        private Integer mallState;

        private Integer stopPurchase;

        private String supplierSku;

        private List<StockInfoList> stockInfoList;
    }

    @Data
    public static class CostInfo {
        private String costPrice;

        private String currency;
    }

    @Data
    public static class StockInfoList {
        private Integer inventoryNum;
    }
}