package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SHEIN商品发布或编辑请求DTO
 */
@Data
public class SheinPublishOrEditRequest {

    /**
     * 类目ID（必填）
     */
    @NotNull(message = "类目ID不能为空")
    @JsonProperty("category_id")
    private Long categoryId;

    /**
     * 产品类型ID（必填）
     */
    @NotNull(message = "产品类型ID不能为空")
    @JsonProperty("product_type_id")
    private Long productTypeId;

    /**
     * 供应商编码（必填）
     */
    @NotEmpty(message = "供应商编码不能为空")
    @JsonProperty("supplier_code")
    private String supplierCode;

    /**
     * 套装标识（0:非套装, 1:套装）
     */
    @JsonProperty("suit_flag")
    private Integer suitFlag;

    /**
     * 多语言名称列表（必填）
     */
    @NotEmpty(message = "多语言名称列表不能为空")
    @JsonProperty("multi_language_name_list")
    private List<MultiLanguageName> multiLanguageNameList;

    /**
     * 产品属性列表（必填）
     */
    @NotEmpty(message = "产品属性列表不能为空")
    @JsonProperty("product_attribute_list")
    private List<ProductAttribute> productAttributeList;

    /**
     * 站点列表（必填）
     */
    @NotEmpty(message = "站点列表不能为空")
    @JsonProperty("site_list")
    private List<SiteInfo> siteList;

    /**
     * SKC列表（必填）
     */
    @NotEmpty(message = "SKC列表不能为空")
    @JsonProperty("skc_list")
    private List<SkcInfo> skcList;

    @Data
    public static class MultiLanguageName {
        /**
         * 语言代码
         */
        @JsonProperty("language")
        private String language;

        /**
         * 商品名称
         */
        @JsonProperty("name")
        private String name;
    }

    @Data
    public static class ProductAttribute {
        /**
         * 属性ID
         */
        @JsonProperty("attribute_id")
        private Long attributeId;
    }

    @Data
    public static class SiteInfo {
        /**
         * 主站点
         */
        @JsonProperty("main_site")
        private String mainSite;

        /**
         * 子站点列表
         */
        @JsonProperty("sub_site_list")
        private List<String> subSiteList;
    }

    @Data
    public static class SkcInfo {
        /**
         * 供应商编码
         */
        @JsonProperty("supplier_code")
        private String supplierCode;

        /**
         * 图片信息
         */
        @JsonProperty("image_info")
        private ImageInfo imageInfo;

        /**
         * 销售属性
         */
        @JsonProperty("sale_attribute")
        private SaleAttribute saleAttribute;

        /**
         * SKU列表
         */
        @JsonProperty("sku_list")
        private List<SkuInfo> skuList;
    }

    @Data
    public static class ImageInfo {
        /**
         * 图片信息列表
         */
        @JsonProperty("image_info_list")
        private List<ImageDetail> imageInfoList;
    }

    @Data
    public static class ImageDetail {
        /**
         * 图片排序
         */
        @JsonProperty("image_sort")
        private Integer imageSort;

        /**
         * 图片类型
         */
        @JsonProperty("image_type")
        private Integer imageType;

        /**
         * 图片URL
         */
        @JsonProperty("image_url")
        private String imageUrl;
    }

    @Data
    public static class SaleAttribute {
        /**
         * 属性ID
         */
        @JsonProperty("attribute_id")
        private Long attributeId;
    }

    @Data
    public static class SkuInfo {
        /**
         * 供应商SKU
         */
        @JsonProperty("supplier_sku")
        private String supplierSku;

        /**
         * 成本信息
         */
        @JsonProperty("cost_info")
        private CostInfo costInfo;

        /**
         * 高度
         */
        @JsonProperty("height")
        private String height;

        /**
         * 长度
         */
        @JsonProperty("length")
        private String length;

        /**
         * 宽度
         */
        @JsonProperty("width")
        private String width;

        /**
         * 重量
         */
        @JsonProperty("weight")
        private String weight;

        /**
         * 商城状态
         */
        @JsonProperty("mall_state")
        private Integer mallState;

        /**
         * 库存信息列表
         */
        @JsonProperty("stock_info_list")
        private List<StockInfo> stockInfoList;
    }

    @Data
    public static class CostInfo {
        /**
         * 成本价格
         */
        @JsonProperty("cost_price")
        private String costPrice;

        /**
         * 货币
         */
        @JsonProperty("currency")
        private String currency;
    }

    @Data
    public static class StockInfo {
        /**
         * 库存数量
         */
        @JsonProperty("inventory_num")
        private Integer inventoryNum;
    }
}