<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.OmsOrderDao">
    <resultMap id="orderDetailResultMap" type="com.macro.mall.dto.OmsOrderDetail" extends="com.macro.mall.mapper.OmsOrderMapper.BaseResultMap">
        <collection property="orderItemList" resultMap="com.macro.mall.mapper.OmsOrderItemMapper.BaseResultMap" columnPrefix="item_"/>
        <collection property="historyList" resultMap="com.macro.mall.mapper.OmsOrderOperateHistoryMapper.BaseResultMap" columnPrefix="history_"/>
    </resultMap>
    <select id="getList" resultMap="com.macro.mall.mapper.OmsOrderMapper.BaseResultMap">
        SELECT id, order_sn, create_time, member_username, total_amount, pay_type, source_type, status
        FROM
        oms_order
        WHERE
        delete_status = 0
        <if test="queryParam.orderSn!=null and queryParam.orderSn!=''">
            AND order_sn = #{queryParam.orderSn}
        </if>
        <if test="queryParam.status!=null">
            AND `status` = #{queryParam.status}
        </if>
        <if test="queryParam.createTime!=null and queryParam.createTime!=''">
            AND create_time LIKE concat(#{queryParam.createTime},"%")
        </if>
        <if test="queryParam.receiverKeyword!=null and queryParam.receiverKeyword!=''">
            AND (
            receiver_name LIKE concat("%",#{queryParam.receiverKeyword},"%")
            OR receiver_phone LIKE concat("%",#{queryParam.receiverKeyword},"%")
            )
        </if>
        <!-- 新增条件：过滤 salesChannelId -->
        <if test="queryParam.salesChannelId != null and queryParam.salesChannelId != ''">
            AND sales_channel_id = #{queryParam.salesChannelId}
        </if>
        <!-- 新增条件：过滤 orderCountryNum -->
        <if test="queryParam.orderCountryNum != null and queryParam.orderCountryNum != ''">
            AND order_country_num = #{queryParam.orderCountryNum}
        </if>
    </select>
    <select id="getListItem" resultMap="com.macro.mall.mapper.OmsOrderItemMapper.BaseResultMap">
        SELECT *
        FROM
        oms_order_item
        WHERE
        delete_status = 0
<!--        &lt;!&ndash; 新增条件：过滤 salesChannelId &ndash;&gt;-->
        <if test="queryParam.warehouseId != null and queryParam.warehouseId != ''">
            AND warehouse_id IN
            <foreach collection="queryParam.warehouseId" item="warehouseId" open="(" separator="," close=")">
                #{warehouseId}
            </foreach>
        </if>
<!--        &lt;!&ndash; 新增条件：过滤 orderCountryNum &ndash;&gt;-->
        <if test="queryParam.location != null and queryParam.location != ''">
            AND location = #{queryParam.location}
        </if>
        <if test="queryParam.parcelId != null and queryParam.parcelId != ''">
            AND parcel_id = #{queryParam.parcelId}
        </if>
        <if test="queryParam.productCode != null and queryParam.productCode != ''">
            AND product_code = #{queryParam.productCode}
        </if>
    </select>
    <select id="getListParcel" resultMap="com.macro.mall.mapper.OmsOrderParcelMapper.BaseResultMap">
        SELECT *
        FROM oms_order_parcel
        <where>
            delete_status = 0
            <if test="queryParam.parcelStatus != null and queryParam.parcelStatus.size() > 0">
                AND parcel_status IN
                <foreach collection="queryParam.parcelStatus" item="parcelStatus" open="(" separator="," close=")">
                    #{parcelStatus}
                </foreach>
            </if>
            <if test="queryParam.warehouseId != null and queryParam.warehouseId.size() > 0">
                AND warehouse_id IN
                <foreach collection="queryParam.warehouseId" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
            <if test="queryParam.location != null and queryParam.location != ''">
                AND location = #{queryParam.location}
            </if>
            <if test="queryParam.parcelSn != null and queryParam.parcelSn != ''">
                AND parcel_sn = #{queryParam.parcelSn}
            </if>
        </where>
    </select>
    <select id="getListOrder" resultMap="com.macro.mall.mapper.OmsOrderMapper.BaseResultMap">
        SELECT *
        FROM
        oms_order_item
        WHERE
        delete_status = 0
        <if test="queryParam.orderSn!=null and queryParam.orderSn!=''">
            AND order_sn = #{queryParam.orderSn}
        </if>
    </select>
    <update id="delivery">
        UPDATE oms_order
        SET
        delivery_sn = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.orderId} THEN #{item.deliverySn}
        </foreach>
        END,
        delivery_company = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.orderId} THEN #{item.deliveryCompany}
        </foreach>
        END,
        delivery_time = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.orderId} THEN now()
        </foreach>
        END,
        `status` = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.orderId} THEN 2
        </foreach>
        END
        WHERE
        id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.orderId}
        </foreach>
        AND `status` = 1
    </update>
    <select id="getDetail" resultMap="orderDetailResultMap">
        SELECT o.*,
            oi.id item_id,
            oi.product_id item_product_id,
            oi.product_sn item_product_sn,
            oi.product_pic item_product_pic,
            oi.product_name item_product_name,
            oi.product_brand item_product_brand,
            oi.product_price item_product_price,
            oi.product_quantity item_product_quantity,
            oi.product_attr item_product_attr,
            oh.id history_id,
            oh.operate_man history_operate_man,
            oh.create_time history_create_time,
            oh.order_status history_order_status,
            oh.note history_note
        FROM
            oms_order o
            LEFT JOIN oms_order_item oi ON o.id = oi.order_id
            LEFT JOIN oms_order_operate_history oh ON o.id = oh.order_id
        WHERE
            o.id = #{id}
        ORDER BY oi.id ASC,oh.create_time DESC
    </select>

</mapper>