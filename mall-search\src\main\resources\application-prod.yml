spring:
  datasource:
    url: ***********************************************************************************************************
    username: reader
    password: 123456
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 #最小空闲连接数
      max-active: 20 #最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" #不统计这些请求数据
      stat-view-servlet: #访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid
  data:
    elasticsearch:
      repositories:
        enabled: true
  elasticsearch:
    uris: es:9200

logging:
  file:
    path: /var/logs
  level:
    root: info
    com.macro.mall: info

logstash:
  host: logstash