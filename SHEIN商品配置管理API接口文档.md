# SHEIN商品配置管理API接口文档

## 接口概述
本文档描述了SHEIN商品配置管理相关的REST API接口，包括创建、查询、更新、删除等操作。

## 基础信息
- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 数据模型

### SheinProductConfig 商品配置对象
```json
{
  "id": "主键ID (Long)",
  "productId": "商品ID (Long, 必填)",
  "sheinCategoryId": "SHEIN类目ID (Long)",
  "sheinProductTypeId": "SHEIN商品类型ID (Long)",
  "spuAttributeId": "SPU属性ID (Long)",
  "skcAttributeId": "SKC属性ID (Long)",
  "mainSite": "主站点 (String)",
  "subSiteList": "子站点列表 (String)",
  "imageUrl": "图片URL (String)",
  "lastPublishedAt": "最后发布时间 (String)",
  "createdAt": "创建时间 (String)",
  "updatedAt": "更新时间 (LocalDateTime)"
}
```

### CommonResult 统一响应格式
```json
{
  "code": "响应码 (200=成功, 500=失败)",
  "message": "响应消息",
  "data": "响应数据"
}
```

## API接口列表

### 1. 创建商品配置

**接口描述**: 为指定商品创建SHEIN配置信息

**请求信息**:
- **请求路径**: `/shein/product/config/create`
- **请求方式**: `POST`
- **Content-Type**: `application/json`

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |
| sheinCategoryId | Long | 否 | SHEIN类目ID |
| sheinProductTypeId | Long | 否 | SHEIN商品类型ID |
| spuAttributeId | Long | 否 | SPU属性ID |
| skcAttributeId | Long | 否 | SKC属性ID |
| mainSite | String | 否 | 主站点 |
| subSiteList | String | 否 | 子站点列表 |
| imageUrl | String | 否 | 图片URL |
| lastPublishedAt | String | 否 | 最后发布时间 |

**请求示例**:
```json
{
  "productId": 12345,
  "sheinCategoryId": 13132,
  "sheinProductTypeId": 9867,
  "spuAttributeId": 1001236,
  "skcAttributeId": 27,
  "mainSite": "shein",
  "subSiteList": "shein-fr,shein-de",
  "imageUrl": "https://example.com/image.jpg",
  "lastPublishedAt": "2024-12-28 10:30:00"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "productId": 12345,
    "sheinCategoryId": 13132,
    "sheinProductTypeId": 9867,
    "spuAttributeId": 1001236,
    "skcAttributeId": 27,
    "mainSite": "shein",
    "subSiteList": "shein-fr,shein-de",
    "imageUrl": "https://example.com/image.jpg",
    "lastPublishedAt": "2024-12-28 10:30:00",
    "createdAt": "2024-12-28 10:30:00",
    "updatedAt": "2024-12-28T10:30:00"
  }
}
```

### 2. 根据商品ID查询配置

**接口描述**: 根据商品ID查询对应的SHEIN配置信息

**请求信息**:
- **请求路径**: `/shein/product/config/get/{productId}`
- **请求方式**: `GET`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

**请求示例**:
```
GET /shein/product/config/get/12345
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "productId": 12345,
    "sheinCategoryId": 13132,
    "sheinProductTypeId": 9867,
    "spuAttributeId": 1001236,
    "skcAttributeId": 27,
    "mainSite": "shein",
    "subSiteList": "shein-fr,shein-de",
    "imageUrl": "https://example.com/image.jpg",
    "lastPublishedAt": "2024-12-28 10:30:00",
    "createdAt": "2024-12-28 10:30:00",
    "updatedAt": "2024-12-28T10:30:00"
  }
}
```

### 3. 根据商品ID更新配置

**接口描述**: 根据商品ID更新对应的SHEIN配置信息

**请求信息**:
- **请求路径**: `/shein/product/config/update/{productId}`
- **请求方式**: `PUT`
- **Content-Type**: `application/json`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

**请求体参数**: 同创建接口，但productId会被路径参数覆盖

**请求示例**:
```json
PUT /shein/product/config/update/12345

{
  "sheinCategoryId": 13133,
  "sheinProductTypeId": 9868,
  "mainSite": "shein-updated",
  "subSiteList": "shein-fr,shein-de,shein-us"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "productId": 12345,
    "sheinCategoryId": 13133,
    "sheinProductTypeId": 9868,
    "spuAttributeId": 1001236,
    "skcAttributeId": 27,
    "mainSite": "shein-updated",
    "subSiteList": "shein-fr,shein-de,shein-us",
    "imageUrl": "https://example.com/image.jpg",
    "lastPublishedAt": "2024-12-28 10:30:00",
    "createdAt": "2024-12-28 10:30:00",
    "updatedAt": "2024-12-28T11:00:00"
  }
}
```

### 4. 根据商品ID删除配置

**接口描述**: 根据商品ID删除对应的SHEIN配置信息

**请求信息**:
- **请求路径**: `/shein/product/config/delete/{productId}`
- **请求方式**: `DELETE`

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

**请求示例**:
```
DELETE /shein/product/config/delete/12345
```

**响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": "删除成功"
}
```

### 5. 分页查询配置列表

**接口描述**: 分页查询所有SHEIN商品配置信息

**请求信息**:
- **请求路径**: `/shein/product/config/list`
- **请求方式**: `GET`

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| pageNum | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 10 | 每页数量 |

**请求示例**:
```
GET /shein/product/config/list?pageNum=1&pageSize=10
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功，总数：25",
  "data": [
    {
      "id": 1,
      "productId": 12345,
      "sheinCategoryId": 13132,
      "sheinProductTypeId": 9867,
      "spuAttributeId": 1001236,
      "skcAttributeId": 27,
      "mainSite": "shein",
      "subSiteList": "shein-fr,shein-de",
      "imageUrl": "https://example.com/image.jpg",
      "lastPublishedAt": "2024-12-28 10:30:00",
      "createdAt": "2024-12-28 10:30:00",
      "updatedAt": "2024-12-28T10:30:00"
    }
  ]
}
```

### 6. 查询所有配置列表

**接口描述**: 查询所有SHEIN商品配置信息（不分页）

**请求信息**:
- **请求路径**: `/shein/product/config/all`
- **请求方式**: `GET`

**请求示例**:
```
GET /shein/product/config/all
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "productId": 12345,
      "sheinCategoryId": 13132,
      "sheinProductTypeId": 9867,
      "spuAttributeId": 1001236,
      "skcAttributeId": 27,
      "mainSite": "shein",
      "subSiteList": "shein-fr,shein-de",
      "imageUrl": "https://example.com/image.jpg",
      "lastPublishedAt": "2024-12-28 10:30:00",
      "createdAt": "2024-12-28 10:30:00",
      "updatedAt": "2024-12-28T10:30:00"
    }
  ]
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 200 | 操作成功 |
| 500 | 操作失败 |

## 常见错误响应

### 参数错误
```json
{
  "code": 500,
  "message": "商品ID不能为空",
  "data": null
}
```

### 数据不存在
```json
{
  "code": 500,
  "message": "未找到该商品的配置信息",
  "data": null
}
```

### 数据已存在
```json
{
  "code": 500,
  "message": "该商品已存在配置，请使用更新接口",
  "data": null
}
```

## 注意事项

1. 所有接口都返回统一的CommonResult格式
2. 创建配置时，如果商品已存在配置，会返回错误提示
3. 更新和删除操作都需要先检查配置是否存在
4. 分页查询按更新时间倒序排列
5. 时间字段格式：`yyyy-MM-dd HH:mm:ss`
6. 建议在生产环境中添加适当的权限验证和限流机制
