package com.macro.mall.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.mapper.*;
import com.macro.mall.model.*;
import com.macro.mall.portal.constants.StatusConstants;
import com.macro.mall.portal.dao.PortalProductDao;
import com.macro.mall.portal.domain.PmsPortalProductDetail;
import com.macro.mall.portal.domain.PmsProductCategoryNode;
import com.macro.mall.portal.dto.PmsPortalProduct;
import com.macro.mall.portal.dto.PmsPortalProductQueryParam;
import com.macro.mall.portal.service.PmsPortalProductService;
import com.macro.mall.portal.service.UmsMemberService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 前台订单管理Service实现类
 * Created by macro on 2020/4/6.
 */
@Service
public class PmsPortalProductServiceImpl implements PmsPortalProductService {
    private static final Logger log = LoggerFactory.getLogger(PmsPortalProductServiceImpl.class);
    @Autowired
    private PmsProductMapper productMapper;
    @Autowired
    private PmsProductCategoryMapper productCategoryMapper;
    @Autowired
    private PmsBrandMapper brandMapper;
    @Autowired
    private PmsProductAttributeMapper productAttributeMapper;
    @Autowired
    private PmsProductAttributeValueMapper productAttributeValueMapper;
    @Autowired
    private PmsSkuStockMapper skuStockMapper;
    @Autowired
    private PmsProductLadderMapper productLadderMapper;
    @Autowired
    private PmsProductFullReductionMapper productFullReductionMapper;
    @Autowired
    private PortalProductDao portalProductDao;
    @Autowired
    private UmsMemberService memberService;
    @Override
    public List<PmsProduct> search(String keyword, Long brandId, Long productCategoryId, Integer pageNum, Integer pageSize, Integer sort) {
        PageHelper.startPage(pageNum, pageSize);
        PmsProductExample example = new PmsProductExample();
        PmsProductExample.Criteria criteria = example.createCriteria();
        criteria.andDeleteStatusEqualTo(0);
        criteria.andPublishStatusEqualTo(1);
        if (StrUtil.isNotEmpty(keyword)) {
            criteria.andNameLike("%" + keyword + "%");
        }
        if (brandId != null) {
            criteria.andBrandIdEqualTo(brandId);
        }
        if (productCategoryId != null) {
            criteria.andProductCategoryIdEqualTo(productCategoryId);
        }
        //1->按新品；2->按销量；3->价格从低到高；4->价格从高到低
        if (sort == 1) {
            example.setOrderByClause("id desc");
        } else if (sort == 2) {
            example.setOrderByClause("sale desc");
        } else if (sort == 3) {
            example.setOrderByClause("price asc");
        } else if (sort == 4) {
            example.setOrderByClause("price desc");
        }
        return productMapper.selectByExample(example);
    }

    @Override
    public List<PmsProductCategoryNode> categoryTreeList() {
        PmsProductCategoryExample example = new PmsProductCategoryExample();
        List<PmsProductCategory> allList = productCategoryMapper.selectByExample(example);
        List<PmsProductCategoryNode> result = allList.stream()
                .filter(item -> item.getParentId().equals(0L))
                .map(item -> covert(item, allList))
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public PmsPortalProductDetail detail(Long id) {
        PmsPortalProductDetail result = new PmsPortalProductDetail();
        //获取商品信息
        PmsProduct product = productMapper.selectByPrimaryKey(id);
        result.setProduct(product);
        //获取品牌信息
        PmsBrand brand = brandMapper.selectByPrimaryKey(product.getBrandId());
        result.setBrand(brand);
        //获取商品属性信息
        PmsProductAttributeExample attributeExample = new PmsProductAttributeExample();
        attributeExample.createCriteria().andProductAttributeCategoryIdEqualTo(product.getProductAttributeCategoryId());
        List<PmsProductAttribute> productAttributeList = productAttributeMapper.selectByExample(attributeExample);
        result.setProductAttributeList(productAttributeList);
        //获取商品属性值信息
        if(CollUtil.isNotEmpty(productAttributeList)){
            List<Long> attributeIds = productAttributeList.stream().map(PmsProductAttribute::getId).collect(Collectors.toList());
            PmsProductAttributeValueExample attributeValueExample = new PmsProductAttributeValueExample();
            attributeValueExample.createCriteria().andProductIdEqualTo(product.getId())
                    .andProductAttributeIdIn(attributeIds);
            List<PmsProductAttributeValue> productAttributeValueList = productAttributeValueMapper.selectByExample(attributeValueExample);
            result.setProductAttributeValueList(productAttributeValueList);
        }
        //获取商品SKU库存信息
        PmsSkuStockExample skuExample = new PmsSkuStockExample();
        skuExample.createCriteria().andProductIdEqualTo(product.getId());
        List<PmsSkuStock> skuStockList = skuStockMapper.selectByExample(skuExample);
        result.setSkuStockList(skuStockList);
        //商品阶梯价格设置
        if(product.getPromotionType()==3){
            PmsProductLadderExample ladderExample = new PmsProductLadderExample();
            ladderExample.createCriteria().andProductIdEqualTo(product.getId());
            List<PmsProductLadder> productLadderList = productLadderMapper.selectByExample(ladderExample);
            result.setProductLadderList(productLadderList);
        }
        //商品满减价格设置
        if(product.getPromotionType()==4){
            PmsProductFullReductionExample fullReductionExample = new PmsProductFullReductionExample();
            fullReductionExample.createCriteria().andProductIdEqualTo(product.getId());
            List<PmsProductFullReduction> productFullReductionList = productFullReductionMapper.selectByExample(fullReductionExample);
            result.setProductFullReductionList(productFullReductionList);
        }
        //商品可用优惠券
        result.setCouponList(portalProductDao.getAvailableCouponList(product.getId(),product.getProductCategoryId()));
        return result;
    }

    public Integer getStockByProductSn(String productSn) {
        PmsProduct product = productMapper.selectByProductSn(productSn);
        if (product != null) {
            log.info("stock:{}", product.getStock());
            return product.getStock();
        } else {
            log.warn("未找到商品SN为{}的库存信息", productSn);
            return null;
        }
    }

    public Integer getStockByProductId(Long productId) {
        log.info("传入的 productId 是: {}", productId);
        PmsProduct product = productMapper.selectByProductId(productId);
        if (product != null) {
            log.info("stock:{}", product.getStock());
            return product.getStock();
        } else {
            log.warn("未找到商品ID为{}的库存信息", productId);
            return null;
        }
    }

    @Override
    public CommonResult<CommonPage<PmsPortalProduct>> list(PmsPortalProductQueryParam portalProductQueryParam, Integer pageSize, Integer pageNum) {
        // 1 验证用户认证
        boolean authenticated = memberService.verifyMemberCredentials(portalProductQueryParam.getUsername(), portalProductQueryParam.getPassword());
        if (!authenticated) {
            return CommonResult.failed("认证失败：用户名或密码错误");
        }
        //TODO 会员等级认证

        // 启动分页插件
        PageHelper.startPage(pageNum, pageSize);

        // 创建查询条件
        PmsProductExample productExample = new PmsProductExample();
        PmsProductExample.Criteria criteria = productExample.createCriteria();

        // 默认条件：查询没有删除的商品
        criteria.andDeleteStatusEqualTo(StatusConstants.CLOSED_STATUS);

        //TODO
        //目前数据库商品未上架、审核未通过，
        // 商品发布状态默认：上架
        //criteria.andPublishStatusEqualTo(StatusConstants.CLOSED_STATUS);

        // 商品审核状态默认：审核通过
        //criteria.andVerifyStatusEqualTo(StatusConstants.CLOSED_STATUS);

        // 如果传入了关键词，进行商品名称模糊查询
        if (!StrUtil.isEmpty(portalProductQueryParam.getKeyword())) {
            criteria.andNameLike("%" + portalProductQueryParam.getKeyword() + "%");
        }

//        // 根据商品货号进行精确查询
//        if (!StrUtil.isEmpty(portalProductQueryParam.getProductSn())) {
//            criteria.andProductSnEqualTo(portalProductQueryParam.getProductSn());
//        }

        // 根据品牌ID进行过滤
        if (portalProductQueryParam.getBrandId() != null) {
            criteria.andBrandIdEqualTo(portalProductQueryParam.getBrandId());
        }

        // 根据商品类别ID进行过滤
        if (portalProductQueryParam.getProductCategoryId() != null) {
            criteria.andProductCategoryIdEqualTo(portalProductQueryParam.getProductCategoryId());
        }
        // 根据商品父级类别ID进行过滤
        if (portalProductQueryParam.getProductCateUplevelId() != null) {
            criteria.andProductCateUplevelIdEqualTo(portalProductQueryParam.getProductCateUplevelId());
        }

        // 根据仓库进行过滤
        if (!StrUtil.isEmpty(portalProductQueryParam.getWarehouseId())) {
            criteria.andWarehouseEqualTo(portalProductQueryParam.getWarehouseId());
        }
        // 根据国家进行过滤
        if (!StrUtil.isEmpty(portalProductQueryParam.getLocation())) {
            criteria.andLocationEqualTo(portalProductQueryParam.getLocation());
        }
        // 根据新品进行过滤
        if (portalProductQueryParam.getNewStatus() != null) {
            criteria.andNewStatusEqualTo(portalProductQueryParam.getNewStatus());
        }
        // 根据推荐进行过滤
        if (portalProductQueryParam.getRecommandStatus() != null) {
            criteria.andRecommandStatusEqualTo(portalProductQueryParam.getRecommandStatus());
        }

        // 根据上架时间进行过滤
        if (portalProductQueryParam.getStartMonth() != null && portalProductQueryParam.getEndMonth() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

            // 解析开始月份
            Calendar startCalendar = Calendar.getInstance();
            startCalendar.setTime(portalProductQueryParam.getStartMonth());
            int startYear = startCalendar.get(Calendar.YEAR);
            int startMonth = startCalendar.get(Calendar.MONTH) + 1;

            // 获取开始月份的第一天（00:00:00）
            startCalendar.set(startYear, startMonth, 1, 0, 0, 0);
            Date startDate = startCalendar.getTime();

            // 解析结束月份
            Calendar endCalendar = Calendar.getInstance();
            endCalendar.setTime(portalProductQueryParam.getEndMonth());
            int endYear = endCalendar.get(Calendar.YEAR);
            int endMonth = endCalendar.get(Calendar.MONTH) + 1;

            // 获取结束月份的最后一天（23:59:59）
            endCalendar.set(endYear, endMonth, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH), 23, 59, 59);
            Date endDate = endCalendar.getTime();

            log.info("筛选上架时间范围: {} - {}", startDate, endDate);

            criteria.andShelfTimeBetween(startDate, endDate);
        }

        // 价格区间过滤
        if (portalProductQueryParam.getMinPrice() != null) {
            criteria.andPriceGreaterThanOrEqualTo(portalProductQueryParam.getMinPrice());
        }
        if (portalProductQueryParam.getMaxPrice() != null) {
            criteria.andPriceLessThanOrEqualTo(portalProductQueryParam.getMaxPrice());
        }
        // 体积区间过滤
        if (portalProductQueryParam.getMinSize() != null) {
            criteria.andSizeGreaterThanOrEqualTo(portalProductQueryParam.getMinSize());
        }
        if (portalProductQueryParam.getMaxSize() != null) {
            criteria.andSizeLessThanOrEqualTo(portalProductQueryParam.getMaxSize());
        }
        // 重量区间过滤
        if (portalProductQueryParam.getMinWeight() != null) {
            criteria.andWeightGreaterThanOrEqualTo(portalProductQueryParam.getMinWeight());
        }
        if (portalProductQueryParam.getMaxWeight() != null) {
            criteria.andWeightLessThanOrEqualTo(portalProductQueryParam.getMaxWeight());
        }

        // 执行查询
        List<PmsProduct> productList = productMapper.selectByExample(productExample);
        for (PmsProduct product : productList) {
            log.info("商品ID: {}, 商品名称: {}, 商品编码: {},国家地区：{},仓库id:{}", product.getId(), product.getName(), product.getProductCode(), product.getLocation(), product.getWarehouseId());
        }
        // 将查询结果映射为 PmsPortalProduct 对象
        // 需要调整返回结果，修改PmsPortalProduct
        List<PmsPortalProduct> pmsPortalProductList = productList.stream().map(product ->
                BeanUtil.copyProperties(product, PmsPortalProduct.class)
        ).collect(Collectors.toList());
        System.out.println("接收到的参数：" + portalProductQueryParam);
        log.info("查询到符合条件的商品数量: {}", pmsPortalProductList.size());


        return CommonResult.success(CommonPage.restPage(pmsPortalProductList));
    }


    @Override
    public PmsProduct getCertificationByProductSn(String productSn) {
        // 假设认证信息存储在 PmsCertification 表中，根据 productSn 查找认证资料
        return productMapper.selectByProductSn(productSn);
    }

    @Override
    public Map<Long, PmsProductWarehouseInfo> getWarehouseInfoByProductIds(List<Long> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            log.info("productIds == null || productIds.isEmpty()");
            return Collections.emptyMap();
        }
        log.info("WarehouseInfoByProductIds");
        // 调用 Mapper 查询仓库信息
        List<PmsProductWarehouseInfo> warehouseInfoList = productMapper.getWarehouseInfoByProductIds(productIds);

        // 转换为 Map 方便按 productId 查询
        return warehouseInfoList.stream()
                .collect(Collectors.toMap(PmsProductWarehouseInfo::getProductId, warehouseInfo -> warehouseInfo));
    }

    /**
     * 初始对象转化为节点对象
     */
    private PmsProductCategoryNode covert(PmsProductCategory item, List<PmsProductCategory> allList) {
        PmsProductCategoryNode node = new PmsProductCategoryNode();
        BeanUtils.copyProperties(item, node);
        List<PmsProductCategoryNode> children = allList.stream()
                .filter(subItem -> subItem.getParentId().equals(item.getId()))
                .map(subItem -> covert(subItem, allList)).collect(Collectors.toList());
        node.setChildren(children);
        return node;
    }
}
