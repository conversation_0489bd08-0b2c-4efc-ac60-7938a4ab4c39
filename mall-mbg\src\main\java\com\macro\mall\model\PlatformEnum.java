package com.macro.mall.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum PlatformEnum {
    SHEIN(1L, "希音"),
    JD(2L, "京东");


    private final Long id;
    private final String name;

    PlatformEnum(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static List<Map<Long, String>> getAllPlatforms() {
        List<Map<Long, String>> list = new ArrayList<>();
        for (PlatformEnum platform : PlatformEnum.values()) {
            Map<Long, String> map = new HashMap<>();
            map.put( platform.getId(), platform.getName());
            list.add(map);
        }
        return list;
    }
}
