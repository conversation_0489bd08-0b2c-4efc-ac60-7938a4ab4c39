<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.UmsAdminRoleRelationDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        INSERT INTO ums_admin_role_relation (admin_id, role_id) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.adminId,jdbcType=BIGINT},
            #{item.roleId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <select id="getRoleList" resultMap="com.macro.mall.mapper.UmsRoleMapper.BaseResultMap">
        select r.*
        from ums_admin_role_relation ar left join ums_role r on ar.role_id = r.id
        where ar.admin_id = #{adminId}
    </select>
    <select id="getResourceList" resultType="com.macro.mall.model.UmsResource">
        SELECT
            ur.id id,
            ur.create_time createTime,
            ur.`name` `name`,
            ur.url url,
            ur.description description,
            ur.category_id categoryId
        FROM
            ums_admin_role_relation ar
        LEFT JOIN ums_role r ON ar.role_id = r.id
        LEFT JOIN ums_role_resource_relation rrr ON r.id = rrr.role_id
        LEFT JOIN ums_resource ur ON ur.id = rrr.resource_id
        WHERE
            ar.admin_id = #{adminId}
        AND ur.id IS NOT NULL
        GROUP BY
            ur.id
    </select>
    <select id="getAdminIdList" resultType="java.lang.Long">
        SELECT
            DISTINCT ar.admin_id
        FROM
            ums_role_resource_relation rr
                LEFT JOIN ums_admin_role_relation ar ON rr.role_id = ar.role_id
        WHERE rr.resource_id=#{resourceId}
    </select>
</mapper>