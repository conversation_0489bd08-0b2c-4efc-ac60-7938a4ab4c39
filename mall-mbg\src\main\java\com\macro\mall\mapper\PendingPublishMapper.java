package com.macro.mall.mapper;

import com.macro.mall.model.PendingPublishEntity;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

public interface PendingPublishMapper {
    List<PendingPublishEntity> selectByUserIdAndProductId(PendingPublishEntity query);

    int updateNumberById(PendingPublishEntity entity);

    int insert(PendingPublishEntity entity);
    @Delete("delete from pms_pending_publish where user_id = #{userId}")
    void deleteByUserId(Long userId);

    int deleteByUserIdAndProductId(Long userId, Long productId);
}
