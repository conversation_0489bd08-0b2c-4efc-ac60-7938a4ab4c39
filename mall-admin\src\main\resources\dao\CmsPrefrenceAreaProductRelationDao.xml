<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.CmsPrefrenceAreaProductRelationDao">
    <insert id="insertList">
        insert into cms_prefrence_area_product_relation (prefrence_area_id, product_id) values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.prefrenceAreaId,jdbcType=BIGINT},
            #{item.productId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>