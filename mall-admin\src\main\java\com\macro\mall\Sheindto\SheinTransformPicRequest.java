package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * SHEIN图片链接转换请求DTO
 */
@Data
public class SheinTransformPicRequest {
    
    /**
     * 图片类型（必填）
     * 1:主图; 2:细节图; 5:方块图; 6:色块图; 7:详情图
     */
    @NotNull(message = "图片类型不能为空")
    @JsonProperty("image_type")
    private Integer imageType;
    
    /**
     * 原始图片链接（必填）
     */
    @NotEmpty(message = "原始图片链接不能为空")
    @JsonProperty("original_url")
    private String originalUrl;
}
