package com.macro.mall.service.impl;

import com.alibaba.excel.EasyExcel;
import com.macro.mall.dto.CusLogisticsBaseDTO;
import com.macro.mall.dto.CusQueryParam;
import com.macro.mall.mapper.CusLogisticsMapper;
import com.macro.mall.model.CusBaseLogistics;
import com.macro.mall.model.CusLogistics;
import com.macro.mall.model.CusLogisticsHistory;
import com.macro.mall.model.UmsRole;
import com.macro.mall.service.CusLogisticsService;
import com.macro.mall.service.OmsOrderService;
import com.macro.mall.service.UmsAdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import java.io.File;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class CusLogisticsServiceImpl implements CusLogisticsService {
    @Autowired
    private UmsAdminService umsAdminService;

    private static final Logger log = LoggerFactory.getLogger(OmsOrderService.class);
    @Autowired
    private CusLogisticsMapper cusLogisticsMapper;

    /**
     * 获取所有物流记录
     * @return 物流记录列表
     */
    @Override
    public List<CusBaseLogistics> getAllLogistics(CusQueryParam queryParam) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        ZoneId zone = ZoneId.of("UTC");

        Long startTimestamp = null;
        Long endTimestamp = null;

        if (queryParam.getStartDate() != null && !queryParam.getStartDate().isEmpty()) {
            startTimestamp = LocalDate.parse(queryParam.getStartDate(), formatter)
                    .atStartOfDay(zone)
                    .toInstant().toEpochMilli();
        }

        if (queryParam.getEndDate() != null && !queryParam.getStartDate().isEmpty()) {
            endTimestamp = LocalDate.parse(queryParam.getEndDate(), formatter)
                    .plusDays(1)
                    .atStartOfDay(zone)
                    .toInstant().toEpochMilli() - 1;
        }

        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        log.info("username:{}", username);
        List<UmsRole> userRoles = umsAdminService.getCurrentUserRole();
        String role = userRoles.get(0).getName();
        String waybillNumber = queryParam.getWaybillNumber();

        String field;
        if ("清关代理公司".equals(role)) {
            field = "cc_companyno";
        } else if ("物流代理公司".equals(role)) {
            field = "fw_logistics_no";
        } else if ("发货代理公司".equals(role)) {
            field = "acceptance_channel_no";
        } else if ("超级管理员".equals(role)) {
            return cusLogisticsMapper.ROOTselectAllLogistics(startTimestamp, endTimestamp, "%" + waybillNumber + "%");
        } else {
            throw new RuntimeException("不支持的角色类型: " + role);
        }

        log.info("当前用户: {}, 角色: {}, 使用字段: {}, 时间范围: {} - {}",
                username, role, field, startTimestamp, endTimestamp);



        return cusLogisticsMapper.selectAllLogistics(field, username, startTimestamp, endTimestamp, "%" + waybillNumber + "%");
    }

    public CusLogistics queryLogistics(Integer id) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        List<UmsRole> userRoles = umsAdminService.getCurrentUserRole();
        String role = userRoles.get(0).getName();

        String field;
        if ("清关代理公司".equals(role)) {
            field = "cc_companyno";
        } else if ("物流代理公司".equals(role)) {
            field = "fw_logistics_no";
        } else if ("发货代理公司".equals(role)) {
            field = "acceptance_channel_no";
        } else if ("超级管理员".equals(role)) {
            return cusLogisticsMapper.ROOTquerySingleLogistics(id);
        } else {
            throw new RuntimeException("不支持的角色类型: " + role);
        }

        return cusLogisticsMapper.querySingleLogistics(field, username, id);
    }

    /**
     * 将 CusLogistics 实体转换为 DTO
     * @param logistics 物流记录实体
     * @return 物流记录 DTO
     */
    private CusLogisticsBaseDTO convertToDto(CusLogistics logistics) {
        CusLogisticsBaseDTO dto = new CusLogisticsBaseDTO();
        BeanUtils.copyProperties(logistics, dto); // 使用 BeanUtils 进行属性拷贝
        return dto;
    }

    @Override
    public int deleteLogisticsByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        // 调用 Mapper 层进行批量删除
        return cusLogisticsMapper.deleteByIds(ids);
    }

    @Override
    public int updateStatusByIds(List<Integer> ids, String status) {

        // 如果传入的 ID 列表为空，则不进行操作
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        return cusLogisticsMapper.updateStatusByIds(ids, status);
    }

    public boolean updateLogisticsNote(Integer id, String note) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        Timestamp trackUpdateTime = getCurrentTimestamp();  // 在 service 层获取时间
        List<UmsRole> userRoles = umsAdminService.getCurrentUserRole();
        String role = userRoles.get(0).getName();

        boolean recordExists = cusLogisticsMapper.getLogisticsId(id);
        if (!recordExists) {
            return false;  // 如果 id 不存在，返回 false 或抛出异常
        }

        cusLogisticsMapper.insertLogisticsHistory(id, note, trackUpdateTime, username, role);

        // 更新当前轨迹
        int rowsAffected = cusLogisticsMapper.updateNoteByKeys(id, note, trackUpdateTime);
        return rowsAffected > 0;
    }

    /**
     * 保存或更新物流记录
     * @param logistics 物流记录
     */
    public void saveOrUpdateLogistics(CusLogistics logistics) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();

        CusLogistics existingLogistics = cusLogisticsMapper.selectByWaybillAndCustomerOrder(
                logistics.getWaybillNumber(), logistics.getCustomerOrderNumber(), logistics.getFwTrackingNumber(), username);

        Timestamp currentTimestamp = getCurrentTimestamp(); // 获取当前时间戳


        if (existingLogistics != null) {
            log.info("logistics to update: {}", logistics);
            log.info("existingLogistics before update: {}", existingLogistics);

            // 如果存在，执行更新，确保只更新不为 null 的字段
            existingLogistics.setTrackUpdateTime(currentTimestamp); // 无论如何更新 trackUpdateTime 为当前时间

            // 使用反射更新其它字段
            updateNonNullFields(existingLogistics, logistics);

            existingLogistics.setAcceptanceChannelNo(username);

            log.info("existingLogistics after update: {}", existingLogistics); // 打印更新后的状态

            cusLogisticsMapper.updateByWaybillAndCustomerOrder(existingLogistics);
        } else {
            log.info("username:{}", username);
            // 如果不存在，执行插入
            logistics.setCreateTime(currentTimestamp); // 设置 create_time 和 track_update_time 为当前时间
            logistics.setTrackUpdateTime(currentTimestamp);
            logistics.setAcceptanceChannelNo(username);
            logistics.setStatus("tracking");
            cusLogisticsMapper.insertLogistics(logistics);
        }
    }

    public List<String> uploadFileAndUpdate(MultipartFile file, List<Integer> ids, String containerNumber,
                                            String dbField, String subDir) {
        List<String> results = new ArrayList<>();

        if (file.isEmpty()) {
            results.add("文件为空，上传失败");
            return results;
        }

        try {
            String uploadDir = "/home/<USER>/server/download/" + subDir;
            String baseUrl = "http://************:8080/cus/download/" + subDir;

            File directory = new File(uploadDir);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 文件名由 containerNumber 决定
            String fileName = containerNumber + "_" + System.currentTimeMillis() + ".xls";
            File destinationFile = new File(uploadDir + fileName);
            file.transferTo(destinationFile);
            String fileUrl = baseUrl + fileName;

            log.info("文件保存至：{}，URL：{}", destinationFile.getAbsolutePath(), fileUrl);

            for (Integer id : ids) {
                CusLogistics logistics = cusLogisticsMapper.findById(id);
                if (logistics != null) {
                    if ("customs_clearance_result".equals(dbField)) {
                        cusLogisticsMapper.updateCustomsClearanceResult(id, fileUrl);
                    } else if ("customs_clearance_materials".equals(dbField)) {
                        cusLogisticsMapper.updateCustomsClearanceMaterials(id, fileUrl);
                    }
                    results.add("ID " + id + " 更新成功");
                } else {
                    results.add("ID " + id + " 未找到对应物流记录");
                }
            }
        } catch (IOException e) {
            log.error("文件上传异常：{}", e.getMessage());
            results.add("文件上传失败：" + e.getMessage());
        }

        return results;
    }

    public List<String> uploadFile(MultipartFile file) {
        List<String> results = new ArrayList<>();

        // 检查文件是否为空
        if (file.isEmpty()) {
            results.add("文件为空，上传失败");
            return results;
        }

        try {
            // 获取当前时间戳并格式化为字符串
            Timestamp timestamp = getCurrentTimestamp();
            String formattedTimestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(timestamp);

            // 获取当前登录用户的用户名
            String username = SecurityContextHolder.getContext().getAuthentication().getName();

            // 定义文件保存路径
            String uploadDir = "/home/<USER>/server/download/temp/";

            // 如果目录不存在，创建该目录
            File directory = new File(uploadDir);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 使用时间戳和用户名生成文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename != null ? originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            String newFileName = username + "_" + formattedTimestamp + fileExtension;

            // 保存文件到服务器指定路径
            File destinationFile = new File(uploadDir + newFileName);
            file.transferTo(destinationFile);

            // 记录日志
            log.info("文件已保存至：{}", destinationFile.getAbsolutePath());

            // 返回成功信息
            results.add("文件上传成功，文件保存路径：" + destinationFile.getAbsolutePath());

        } catch (IOException e) {
            log.error("文件上传异常：{}", e.getMessage());
            results.add("文件上传失败：" + e.getMessage());
        }

        return results;
    }

    public List<CusLogisticsHistory> getLogisticsHistoryByLogisticsId(Long logisticsId) {
        return cusLogisticsMapper.getLogisticsHistoryByLogisticsId(logisticsId);
    }

    /**
     * 获取当前时间戳
     * @return 当前时间戳
     */
    private Timestamp getCurrentTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }



    // 使用反射更新非 null 字段
    private void updateNonNullFields(CusLogistics existingLogistics, CusLogistics logistics) {
        Field[] fields = CusLogistics.class.getDeclaredFields(); // 获取所有字段

        for (Field field : fields) {
            field.setAccessible(true); // 设置字段可访问

            try {
                Object newValue = field.get(logistics); // 获取 logistics 中的字段值

                // 只有在 logistics 中该字段不为 null 时才更新
                if (newValue != null) {
                    Object oldValue = field.get(existingLogistics); // 获取更新前的值

                    if (oldValue == null || !oldValue.equals(newValue)) {
                        // 记录更新的字段和值
                        log.info("Updating field: {}. Old value: {}, New value: {}", field.getName(), oldValue, newValue);
                    }

                    field.set(existingLogistics, newValue); // 更新字段值
                }
            } catch (IllegalAccessException e) {
                log.error("Error accessing field: {}", field.getName(), e);
            }
        }
    }
}
