package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SHEIN商家仓库列表响应DTO
 */
@Data
public class SheinWarehouseList {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 仓库信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    @JsonProperty("traceId")
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 仓库列表
         */
        private List<WarehouseInfo> list;
    }
    
    @Data
    public static class WarehouseInfo {
        /**
         * 仓库编码
         */
        @JsonProperty("warehouseCode")
        private String warehouseCode;
        
        /**
         * 仓库名称
         */
        @JsonProperty("warehouseName")
        private String warehouseName;
        
        /**
         * 销售国家列表
         */
        @JsonProperty("saleCountryList")
        private List<String> saleCountryList;
        
        /**
         * 创建类型
         * 1: 自主运营
         * 3: 半托管
         */
        @JsonProperty("createType")
        private Integer createType;
        
        /**
         * 仓库类型
         * 1: 普通仓库
         */
        @JsonProperty("warehouseType")
        private Integer warehouseType;
        
        /**
         * 授权服务编码
         */
        @JsonProperty("authServiceCode")
        private String authServiceCode;
        
        /**
         * 授权服务名称
         */
        @JsonProperty("authServiceName")
        private String authServiceName;
    }
}
