package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * SHEIN查询商品审核状态请求DTO
 */
@Data
public class SheinQueryDocumentStateRequest {
    
    /**
     * SPU列表（必填）
     */
    @NotEmpty(message = "SPU列表不能为空")
    @JsonProperty("spuList")
    private List<SpuInfo> spuList;
    
    @Data
    public static class SpuInfo {
        /**
         * SPU名称（必填）
         */
        @JsonProperty("spuName")
        private String spuName;
        
        /**
         * 版本号（可选）
         */
        @JsonProperty("version")
        private String version;
    }
}
