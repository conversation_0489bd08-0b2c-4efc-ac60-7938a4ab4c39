<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.PmsProductAttributeMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.PmsProductAttribute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_attribute_category_id" jdbcType="BIGINT" property="productAttributeCategoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="select_type" jdbcType="INTEGER" property="selectType" />
    <result column="input_type" jdbcType="INTEGER" property="inputType" />
    <result column="input_list" jdbcType="VARCHAR" property="inputList" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="filter_type" jdbcType="INTEGER" property="filterType" />
    <result column="search_type" jdbcType="INTEGER" property="searchType" />
    <result column="related_status" jdbcType="INTEGER" property="relatedStatus" />
    <result column="hand_add_status" jdbcType="INTEGER" property="handAddStatus" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_attribute_category_id, name, select_type, input_type, input_list, sort, 
    filter_type, search_type, related_status, hand_add_status, type
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.PmsProductAttributeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pms_product_attribute
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pms_product_attribute
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pms_product_attribute
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.PmsProductAttributeExample">
    delete from pms_product_attribute
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.PmsProductAttribute">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_product_attribute (product_attribute_category_id, name, 
      select_type, input_type, input_list, 
      sort, filter_type, search_type, 
      related_status, hand_add_status, type
      )
    values (#{productAttributeCategoryId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{selectType,jdbcType=INTEGER}, #{inputType,jdbcType=INTEGER}, #{inputList,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{filterType,jdbcType=INTEGER}, #{searchType,jdbcType=INTEGER}, 
      #{relatedStatus,jdbcType=INTEGER}, #{handAddStatus,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.PmsProductAttribute">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pms_product_attribute
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productAttributeCategoryId != null">
        product_attribute_category_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="selectType != null">
        select_type,
      </if>
      <if test="inputType != null">
        input_type,
      </if>
      <if test="inputList != null">
        input_list,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="filterType != null">
        filter_type,
      </if>
      <if test="searchType != null">
        search_type,
      </if>
      <if test="relatedStatus != null">
        related_status,
      </if>
      <if test="handAddStatus != null">
        hand_add_status,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productAttributeCategoryId != null">
        #{productAttributeCategoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="selectType != null">
        #{selectType,jdbcType=INTEGER},
      </if>
      <if test="inputType != null">
        #{inputType,jdbcType=INTEGER},
      </if>
      <if test="inputList != null">
        #{inputList,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="filterType != null">
        #{filterType,jdbcType=INTEGER},
      </if>
      <if test="searchType != null">
        #{searchType,jdbcType=INTEGER},
      </if>
      <if test="relatedStatus != null">
        #{relatedStatus,jdbcType=INTEGER},
      </if>
      <if test="handAddStatus != null">
        #{handAddStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.PmsProductAttributeExample" resultType="java.lang.Long">
    select count(*) from pms_product_attribute
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pms_product_attribute
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productAttributeCategoryId != null">
        product_attribute_category_id = #{record.productAttributeCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.selectType != null">
        select_type = #{record.selectType,jdbcType=INTEGER},
      </if>
      <if test="record.inputType != null">
        input_type = #{record.inputType,jdbcType=INTEGER},
      </if>
      <if test="record.inputList != null">
        input_list = #{record.inputList,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.filterType != null">
        filter_type = #{record.filterType,jdbcType=INTEGER},
      </if>
      <if test="record.searchType != null">
        search_type = #{record.searchType,jdbcType=INTEGER},
      </if>
      <if test="record.relatedStatus != null">
        related_status = #{record.relatedStatus,jdbcType=INTEGER},
      </if>
      <if test="record.handAddStatus != null">
        hand_add_status = #{record.handAddStatus,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pms_product_attribute
    set id = #{record.id,jdbcType=BIGINT},
      product_attribute_category_id = #{record.productAttributeCategoryId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      select_type = #{record.selectType,jdbcType=INTEGER},
      input_type = #{record.inputType,jdbcType=INTEGER},
      input_list = #{record.inputList,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      filter_type = #{record.filterType,jdbcType=INTEGER},
      search_type = #{record.searchType,jdbcType=INTEGER},
      related_status = #{record.relatedStatus,jdbcType=INTEGER},
      hand_add_status = #{record.handAddStatus,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.PmsProductAttribute">
    update pms_product_attribute
    <set>
      <if test="productAttributeCategoryId != null">
        product_attribute_category_id = #{productAttributeCategoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="selectType != null">
        select_type = #{selectType,jdbcType=INTEGER},
      </if>
      <if test="inputType != null">
        input_type = #{inputType,jdbcType=INTEGER},
      </if>
      <if test="inputList != null">
        input_list = #{inputList,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="filterType != null">
        filter_type = #{filterType,jdbcType=INTEGER},
      </if>
      <if test="searchType != null">
        search_type = #{searchType,jdbcType=INTEGER},
      </if>
      <if test="relatedStatus != null">
        related_status = #{relatedStatus,jdbcType=INTEGER},
      </if>
      <if test="handAddStatus != null">
        hand_add_status = #{handAddStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.PmsProductAttribute">
    update pms_product_attribute
    set product_attribute_category_id = #{productAttributeCategoryId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      select_type = #{selectType,jdbcType=INTEGER},
      input_type = #{inputType,jdbcType=INTEGER},
      input_list = #{inputList,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      filter_type = #{filterType,jdbcType=INTEGER},
      search_type = #{searchType,jdbcType=INTEGER},
      related_status = #{relatedStatus,jdbcType=INTEGER},
      hand_add_status = #{handAddStatus,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>