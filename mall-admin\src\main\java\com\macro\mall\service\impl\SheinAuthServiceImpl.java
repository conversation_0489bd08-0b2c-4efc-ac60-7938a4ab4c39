package com.macro.mall.service.impl;

import com.macro.mall.client.SheinApiClient;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.config.SheinConfig;
import com.macro.mall.constants.SheinApiPath;
import com.macro.mall.dto.SheinAuthCallbackDTO;
import com.macro.mall.dto.SheinAuthPrepareDTO;
import com.macro.mall.mapper.SheinAuthMapper;
import com.macro.mall.model.SheinAuthEntity;
import com.macro.mall.model.UmsAdmin;
import com.macro.mall.service.SheinAuthService;
import com.macro.mall.service.UmsAdminService;
import com.macro.mall.util.SheinKeywordCryptoUtil;
import com.macro.mall.util.SheinSignUtil;
import com.macro.mall.vo.AuthResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
public class SheinAuthServiceImpl implements SheinAuthService {
    @Autowired
    private SheinConfig sheinConfig;
    @Autowired
    private SheinApiClient sheinApiClient;
    @Autowired
    private SheinAuthMapper sheinAuthMapper;
    @Autowired
    private UmsAdminService umsAdminService;

    @Override
    public CommonResult<SheinAuthPrepareDTO> prepareAuthLink() {
        String baseUrl = sheinConfig.getAuthUrl(); // https://openapi-sem.sheincorp.com/#/empower
        String appId = sheinConfig.getAppId();
        String redirect = Base64.getEncoder().encodeToString(sheinConfig.getRedirectUrl().getBytes(StandardCharsets.UTF_8));
        //可用于回调时候的校验是否同一流程
        String UserName = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(UserName);
        if (admin == null) {
            return CommonResult.failed("用户不存在");
        }
        Long adminId = admin.getId();
        //将adminId保存在state中

        String state = adminId.toString();

        String fullUrl = String.format("%s?appid=%s&redirectUrl=%s&state=%s", baseUrl, appId, redirect, state);
        SheinAuthPrepareDTO sheinAuthPrepareDTO = new SheinAuthPrepareDTO(fullUrl, state);
        return CommonResult.success(sheinAuthPrepareDTO);
    }

    @Override
    public CommonResult handleCallback(SheinAuthCallbackDTO dto) {
        // 1. 校验参数
        if (dto.getTempToken() == null || dto.getState() == null || dto.getAppid() == null) {
            return CommonResult.validateFailed("缺少必要参数");
        }
        // 获取当前用户
        String state = dto.getState();
        //将state转换为用户ID
        Long adminId = Long.parseLong(state);
        //判断是否已授权
//        SheinAuthEntity sheinAuthDO = sheinAuthMapper.selectByUserId(adminId);
//        if (sheinAuthDO != null) {
//            return CommonResult.failed("用户已授权");
//        }

        // 2. 获取 AppId 与签名
        String appId = sheinConfig.getAppId();
        if (!appId.equals(dto.getAppid())) {
            return CommonResult.failed("appId 不一致");
        }
        String appSecret = sheinConfig.getAppSecret();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomKey = UUID.randomUUID().toString().substring(0, 5);
        // 生成签名
        String sign = null;
        try {
            sign = SheinSignUtil.generateSheinSignature(appId, appSecret, SheinApiPath.GET_BY_TOKEN_API_PATH ,timestamp,randomKey);
        } catch (Exception e) {
            return CommonResult.failed("签名生成失败");
        }

        try {
            // 3. 调用 shein 授权交换接口，返回的是 JsonNode
            CommonResult result = sheinApiClient.getByToken(dto.getTempToken(), appId, timestamp, sign);

            // 解析返回结构里的内容
            if(result.getCode() != 200 ){
                return CommonResult.failed("授权失败");
            }
            AuthResultVo authResultVo = (AuthResultVo) result.getData();
            if (authResultVo == null) {
                throw new RuntimeException("授权返回结果缺少 data 节点");
            }
            // 获取 openKeyId 与 secretKey
            String openKeyId = authResultVo.getOpenKeyId();
            //encryptedSecretKey为密文，appSecret为密钥
            String encryptedSecretKey = authResultVo.getSecretKey();

            if (openKeyId == null || encryptedSecretKey == null) {
                throw new RuntimeException("openKeyId 或 secretKey 缺失");
            }

            // 4. 解密 secretKey
            String decryptedSecretKey = SheinKeywordCryptoUtil.decrypt(encryptedSecretKey, appSecret);

            // 5. 存储授权信息
            SheinAuthEntity auth = new SheinAuthEntity();
            auth.setUserId(adminId);
            auth.setSupplierId(authResultVo.getSupplierId());
            auth.setOpenKeyId(openKeyId);
            auth.setSecretKey(encryptedSecretKey);
            auth.setCreateTime(new Date());
            auth.setUpdateTime(new Date());
            auth.setValid(true);
            auth.setLanguage("zh-cn");
            // 插入数据库
            int insertOrUpdate = sheinAuthMapper.insertOrUpdate(auth);
            if (insertOrUpdate > 0) {
                log.info("授权成功");
            }else{
                log.info("授权失败");
                return CommonResult.failed("数据库插入失败");
            }

        } catch (IOException e) {
            // 网络请求异常，建议抛出或者记录日志
            throw new RuntimeException("调用SHEIN接口失败", e);
        }
        return CommonResult.success("API授权成功");
    }
}
