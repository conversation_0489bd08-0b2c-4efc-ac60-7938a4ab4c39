package com.macro.mall.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dto.JsonUtil;
import com.macro.mall.model.WmsWarehouse;
import com.macro.mall.service.AdminLogService;
import com.macro.mall.service.WmsWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@RestController
@Api(tags = "WmsWarehouseController")
@Tag(name = "WmsWarehouseController", description = "后台仓库管理")
@RequestMapping("/warehouses")
public class WmsWarehouseController {

    @Autowired
    private WmsWarehouseService wmsWarehouseService;
    @Autowired
    private AdminLogService adminLogService;

    /**
     * 获取所有仓库的详细信息
     */
    @ApiOperation(value = "获取所有仓库的详细信息")
    @GetMapping("/AllList")
    public CommonResult<List<WmsWarehouse>> getAllWarehouses(HttpServletRequest request) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "GET";
        String operation = "获取所有仓库的详细信息";
        Date now = new Date();

        try {
            List<WmsWarehouse> warehouses = wmsWarehouseService.getAllWarehouses();

            adminLogService.saveApiLog(username, apiPath, httpMethod, operation, "无请求参数", ip, now);

            return CommonResult.success(warehouses);
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", "无请求参数", ip, now);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据仓库ID获取仓库的详细信息
     */
    @ApiOperation(value = "根据仓库ID获取仓库的详细信息")
    @GetMapping("/List")
    public CommonResult<List<WmsWarehouse>> getWarehousesByIds(HttpServletRequest request,
                                                               @RequestParam List<Long> warehouseIds) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        String ip = request.getRemoteAddr();
        String apiPath = request.getRequestURI();
        String httpMethod = "GET";
        String operation = "根据仓库ID获取仓库详细信息";
        Date now = new Date();

        String requestParams = JsonUtil.toJson(warehouseIds);

        try {
            Optional<List<WmsWarehouse>> warehouses = wmsWarehouseService.getWarehousesByIds(warehouseIds);

            if (warehouses.isPresent() && !warehouses.get().isEmpty()) {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation, requestParams, ip, now);
                return CommonResult.success(warehouses.get());
            } else {
                adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（未找到仓库）", requestParams, ip, now);
                return CommonResult.failed("未找到任何仓库");
            }
        } catch (Exception e) {
            adminLogService.saveApiLog(username, apiPath, httpMethod, operation + "（异常）", requestParams, ip, now);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

}
