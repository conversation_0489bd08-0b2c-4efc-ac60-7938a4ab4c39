package com.macro.mall.controller;

import com.macro.mall.Sheindto.*;
import com.macro.mall.dto.SheinProductCreateDTO;
import com.macro.mall.dto.SheinProductResultDTO;
import com.macro.mall.service.SheinProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Api(tags = "SHEIN商品管理")
@RestController
@RequestMapping("/shein/product")
public class SheinProductController {
    @Resource
    private SheinProductService sheinProductService;

    @ApiOperation("商品一键上架SHEIN")
    @PostMapping("/create")
    public SheinProductResultDTO createProduct(@RequestBody SheinProductCreateDTO productDTO) {
        return sheinProductService.createProduct(productDTO);
    }

    @ApiOperation("获取商品发布规范")
    @PostMapping("/publish-spec")
    public SheinPublishSpec getPublishSpec(
            @RequestBody(required = false) SheinPublishSpecRequest request,
            @RequestHeader("Authorization") String token) {
        // 如果请求体为空，创建一个空的请求对象
        if (request == null) {
            request = new SheinPublishSpecRequest();
        }
        return sheinProductService.getPublishSpec(request);
    }

    @ApiOperation("获取可用品牌列表")
    @PostMapping("/brands")
    public SheinBrandList getBrands(@RequestHeader("Authorization") String token) {
        return sheinProductService.getBrands();
    }

    @ApiOperation("获取类目树")
    @PostMapping("/categories")
    public SheinCategoryTree getCategoryTree(@RequestHeader("Authorization") String token) {
        return sheinProductService.getCategoryTree();
    }

    @ApiOperation("查询店铺可选属性")
    @PostMapping("/attribute-template")
    public SheinAttributeTemplate getAttributeTemplate(
            @RequestBody SheinAttributeTemplateRequest request,
            @RequestHeader("Authorization") String token) {
        return sheinProductService.getAttributeTemplate(request);
    }

    @ApiOperation("查询是否支持自定义属性值")
    @PostMapping("/custom-attribute-permission")
    public SheinCustomAttributePermission getCustomAttributePermission(
            @RequestBody SheinCustomAttributePermissionRequest request,
            @RequestHeader("Authorization") String token) {
        return sheinProductService.getCustomAttributePermission(request);
    }

    @ApiOperation("添加自定义属性值")
    @PostMapping("/add-custom-attribute-value")
    public SheinAddCustomAttributeValue addCustomAttributeValue(
            @RequestBody SheinAddCustomAttributeValueRequest request,
            @RequestHeader("Authorization") String token) {
        return sheinProductService.addCustomAttributeValue(request);
    }

    @ApiOperation("本地图片上传")
    @PostMapping("/upload-pic")
    public SheinUploadPic uploadPic(
            @RequestParam("image_type") Integer imageType,
            @RequestParam("file") MultipartFile file,
            @RequestHeader("Authorization") String token) {
        SheinUploadPicRequest request = new SheinUploadPicRequest();
        request.setImageType(imageType);
        request.setFile(file);
        return sheinProductService.uploadPic(request);
    }

    @ApiOperation("图片链接转换")
    @PostMapping("/transform-pic")
    public SheinTransformPic transformPic(
            @RequestBody SheinTransformPicRequest request,
            @RequestHeader("Authorization") String token) {
        return sheinProductService.transformPic(request);
    }

    @ApiOperation("查询店铺可售站点")
    @PostMapping("/site-list")
    public SheinSiteList getSiteList(
            @RequestBody SheinSiteListRequest request,
            @RequestHeader("Authorization") String token) {
        return sheinProductService.getSiteList(request);
    }

    @ApiOperation("查询商家仓库列表(自主运营和半托管模式)")
    @GetMapping("/warehouse-list")
    public SheinWarehouseList getWarehouseList(@RequestHeader("Authorization") String token) {
        SheinWarehouseListRequest request = new SheinWarehouseListRequest();
        return sheinProductService.getWarehouseList(request);
    }


}
