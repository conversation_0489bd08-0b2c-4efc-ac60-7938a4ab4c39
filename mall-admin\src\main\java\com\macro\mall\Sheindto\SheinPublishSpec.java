package com.macro.mall.Sheindto;

import lombok.Data;

import java.util.List;

/**
 * SHEIN商品发布规范响应DTO
 */
@Data
public class SheinPublishSpec {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 填写标准列表
         */
        private List<FillInStandard> fillInStandardList;
        
        /**
         * 默认语言
         */
        private String defaultLanguage;
        
        /**
         * 图片配置列表
         */
        private List<PictureConfig> pictureConfigList;
        
        /**
         * 货币
         */
        private String currency;
        
        /**
         * 是否支持销售属性排序
         */
        private Boolean supportSaleAttributeSort;
    }
    
    @Data
    public static class FillInStandard {
        /**
         * 模块名称
         */
        private String module;
        
        /**
         * 字段键
         */
        private String fieldKey;
        
        /**
         * 是否必填
         */
        private Boolean required;
        
        /**
         * 是否显示
         */
        private Boolean show;
    }
    
    @Data
    public static class PictureConfig {
        /**
         * 图片类型
         */
        private String pictureType;
        
        /**
         * 最小数量
         */
        private Integer minCount;
        
        /**
         * 最大数量
         */
        private Integer maxCount;
        
        /**
         * 图片要求
         */
        private String requirement;
    }
}
