package com.macro.mall.portal.controller;

import com.macro.mall.common.api.CommonPage;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.model.PmsCertification;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.portal.domain.PmsPortalProductDetail;
import com.macro.mall.portal.domain.PmsProductCategoryNode;
import com.macro.mall.portal.domain.StockQueryParam;
import com.macro.mall.portal.dto.PmsPortalProduct;
import com.macro.mall.portal.dto.PmsPortalProductQueryParam;
import com.macro.mall.portal.service.PmsPortalProductService;
import com.macro.mall.portal.service.UmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 前台商品管理Controller
 * Created by macro on 2020/4/6.
 */
@Controller
@Api(tags = "PmsPortalProductController")
@Tag(name = "PmsPortalProductController", description = "前台商品管理")
@RequestMapping("/product")
public class PmsPortalProductController {
    @Autowired
    private UmsMemberService memberService;
    @Autowired
    private PmsPortalProductService portalProductService;

    @ApiOperation(value = "综合搜索、筛选、排序")
    @ApiImplicitParam(name = "sort", value = "排序字段:0->按相关度；1->按新品；2->按销量；3->价格从低到高；4->价格从高到低",
            defaultValue = "0", allowableValues = "0,1,2,3,4", paramType = "query", dataType = "integer")
    @RequestMapping(value = "/search", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<PmsProduct>> search(@RequestParam(required = false) String keyword,
                                                       @RequestParam(required = false) Long brandId,
                                                       @RequestParam(required = false) Long productCategoryId,
                                                       @RequestParam(required = false, defaultValue = "0") Integer pageNum,
                                                       @RequestParam(required = false, defaultValue = "5") Integer pageSize,
                                                       @RequestParam(required = false, defaultValue = "0") Integer sort) {
        List<PmsProduct> productList = portalProductService.search(keyword, brandId, productCategoryId, pageNum, pageSize, sort);
        return CommonResult.success(CommonPage.restPage(productList));
    }

    @ApiOperation("以树形结构获取所有商品分类")
    @RequestMapping(value = "/categoryTreeList", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<List<PmsProductCategoryNode>> categoryTreeList() {
        List<PmsProductCategoryNode> list = portalProductService.categoryTreeList();
        return CommonResult.success(list);
    }

    @ApiOperation("获取前台商品详情")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<PmsPortalProductDetail> detail(@PathVariable Long id) {
        PmsPortalProductDetail productDetail = portalProductService.detail(id);
        return CommonResult.success(productDetail);
    }

    @ApiOperation("根据商品ID获取库存信息")
    @RequestMapping(value = "/stock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<Integer> getStockByProductSn(@RequestBody StockQueryParam param) {
        boolean authenticated = memberService.verifyMemberCredentials(param.getUsername(), param.getPassword());
        if (!authenticated) {
            return CommonResult.failed("认证失败：用户名或密码错误");
        }

        Integer stock = portalProductService.getStockByProductId(param.getProductId());
        if (stock != null) {
            return CommonResult.success(stock);
        } else {
            return CommonResult.failed("未找到该商品的库存信息");
        }
    }

    @ApiOperation("根据商品SN获取商品认证资料")
    @RequestMapping(value = "/certification/{productSn}", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<PmsProduct> getCertificationByProductSn(@PathVariable String productSn) {
        PmsProduct certification = portalProductService.getCertificationByProductSn(productSn);

        if (certification == null) {
            return CommonResult.failed("未找到该商品的认证资料");
        }

        return CommonResult.success(certification);
    }
    @ApiOperation("查询商品")
    @GetMapping("/list")
    @ResponseBody
    public CommonResult<CommonPage<PmsPortalProduct>> getList(HttpServletRequest request,
                                                        PmsPortalProductQueryParam portalProductQueryParam,
                                                        @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        // 调用业务逻辑
        //List<PmsPortalProduct> productList = portalProductService.list(portalProductQueryParam, pageSize, pageNum);
        return portalProductService.list(portalProductQueryParam, pageSize, pageNum);
    }
}
