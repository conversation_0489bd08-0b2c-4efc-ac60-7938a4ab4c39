package com.macro.mall.Sheindto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * SHEIN商品发布或编辑响应DTO
 */
@Data
public class SheinPublishOrEditResponse {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 发布结果信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    @JsonProperty("traceId")
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 发布是否成功
         */
        private Boolean success;
        
        /**
         * SPU名称
         */
        @JsonProperty("spu_name")
        private String spuName;
        
        /**
         * SKC列表
         */
        @JsonProperty("skc_list")
        private List<SkcResult> skcList;
        
        /**
         * 版本号
         */
        private String version;
        
        /**
         * 预验证结果
         */
        @JsonProperty("pre_valid_result")
        private Object preValidResult;
        
        /**
         * MCC验证结果
         */
        @JsonProperty("mcc_valid_result")
        private Object mccValidResult;
        
        /**
         * 额外信息
         */
        private Map<String, Object> extra;
    }
    
    @Data
    public static class SkcResult {
        /**
         * SKC名称
         */
        @JsonProperty("skc_name")
        private String skcName;
        
        /**
         * SKU列表
         */
        @JsonProperty("sku_list")
        private List<SkuResult> skuList;
    }
    
    @Data
    public static class SkuResult {
        /**
         * SKU编码
         */
        @JsonProperty("sku_code")
        private String skuCode;
        
        /**
         * 供应商SKU
         */
        @JsonProperty("supplier_sku")
        private String supplierSku;
    }
}
