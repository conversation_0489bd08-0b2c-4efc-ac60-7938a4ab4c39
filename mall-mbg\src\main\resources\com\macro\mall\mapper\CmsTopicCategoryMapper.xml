<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsTopicCategoryMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsTopicCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="subject_count" jdbcType="INTEGER" property="subjectCount" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, icon, subject_count, show_status, sort
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsTopicCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_topic_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cms_topic_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_topic_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsTopicCategoryExample">
    delete from cms_topic_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsTopicCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic_category (name, icon, subject_count, 
      show_status, sort)
    values (#{name,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, #{subjectCount,jdbcType=INTEGER}, 
      #{showStatus,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsTopicCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="subjectCount != null">
        subject_count,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="subjectCount != null">
        #{subjectCount,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsTopicCategoryExample" resultType="java.lang.Long">
    select count(*) from cms_topic_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_topic_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.subjectCount != null">
        subject_count = #{record.subjectCount,jdbcType=INTEGER},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_topic_category
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      icon = #{record.icon,jdbcType=VARCHAR},
      subject_count = #{record.subjectCount,jdbcType=INTEGER},
      show_status = #{record.showStatus,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsTopicCategory">
    update cms_topic_category
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="subjectCount != null">
        subject_count = #{subjectCount,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsTopicCategory">
    update cms_topic_category
    set name = #{name,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      subject_count = #{subjectCount,jdbcType=INTEGER},
      show_status = #{showStatus,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>