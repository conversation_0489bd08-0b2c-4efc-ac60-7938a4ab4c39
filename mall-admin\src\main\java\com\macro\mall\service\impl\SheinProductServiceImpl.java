package com.macro.mall.service.impl;

import com.macro.mall.Sheindto.*;
import com.macro.mall.client.SheinApiClient;
import com.macro.mall.dto.SheinProductCreateDTO;
import com.macro.mall.dto.SheinProductResultDTO;
import com.macro.mall.mapper.SheinAuthMapper;
import com.macro.mall.model.SheinAuthEntity;
import com.macro.mall.service.SheinProductService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SheinProductServiceImpl implements SheinProductService {

    @Resource
    private SheinAuthMapper sheinAuthMapper;

    @Resource
    private SheinApiClient sheinApiClient;

    @Override
    public SheinProductResultDTO createProduct(SheinProductCreateDTO productDTO) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return fail("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN商品创建接口
        try {
            return sheinApiClient.createProduct(authDO.getOpenKeyId(), authDO.getSecretKey(), productDTO);
        } catch (Exception e) {
            e.printStackTrace();
            return fail("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinPublishSpec getPublishSpec(SheinPublishSpecRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedPublishSpec("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN获取发布规范接口
        try {
            return sheinApiClient.getPublishSpec(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedPublishSpec("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinBrandList getBrands() {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedBrandList("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN获取品牌列表接口
        try {
            return sheinApiClient.getBrands(authDO.getOpenKeyId(), authDO.getSecretKey());
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedBrandList("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinCategoryTree getCategoryTree() {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedCategoryTree("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN获取类目树接口
        try {
            return sheinApiClient.getCategoryTree(authDO.getOpenKeyId(), authDO.getSecretKey());
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedCategoryTree("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinAttributeTemplate getAttributeTemplate(SheinAttributeTemplateRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedAttributeTemplate("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN查询店铺可选属性接口
        try {
            return sheinApiClient.getAttributeTemplate(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedAttributeTemplate("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinCustomAttributePermission getCustomAttributePermission(SheinCustomAttributePermissionRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedCustomAttributePermission("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN查询是否支持自定义属性值接口
        try {
            return sheinApiClient.getCustomAttributePermission(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedCustomAttributePermission("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinAddCustomAttributeValue addCustomAttributeValue(SheinAddCustomAttributeValueRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedAddCustomAttributeValue("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN添加自定义属性值接口
        try {
            return sheinApiClient.addCustomAttributeValue(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedAddCustomAttributeValue("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinUploadPic uploadPic(SheinUploadPicRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedUploadPic("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN本地图片上传接口
        try {
            return sheinApiClient.uploadPic(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedUploadPic("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinTransformPic transformPic(SheinTransformPicRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedTransformPic("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN图片链接转换接口
        try {
            return sheinApiClient.transformPic(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedTransformPic("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    @Override
    public SheinSiteList getSiteList(SheinSiteListRequest request) {
        // 1. 查询当前用户的openKeyId和secretKey
        Long userId = 1L; // TODO: 从上下文获取登录用户ID
        SheinAuthEntity authDO = sheinAuthMapper.selectByUserId(userId);
        if (authDO == null || authDO.getSecretKey() == null) {
            return createFailedSiteList("用户未授权SHEIN");
        }

        // 2. 调用客户端请求SHEIN查询店铺可售站点接口
        try {
            return sheinApiClient.getSiteList(authDO.getOpenKeyId(), authDO.getSecretKey(), request);
        } catch (Exception e) {
            e.printStackTrace();
            return createFailedSiteList("调用SHEIN接口失败: " + e.getMessage());
        }
    }

    private SheinProductResultDTO fail(String message) {
        SheinProductResultDTO result = new SheinProductResultDTO();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    private SheinPublishSpec createFailedPublishSpec(String message) {
        SheinPublishSpec result = new SheinPublishSpec();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinBrandList createFailedBrandList(String message) {
        SheinBrandList result = new SheinBrandList();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinCategoryTree createFailedCategoryTree(String message) {
        SheinCategoryTree result = new SheinCategoryTree();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinAttributeTemplate createFailedAttributeTemplate(String message) {
        SheinAttributeTemplate result = new SheinAttributeTemplate();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinCustomAttributePermission createFailedCustomAttributePermission(String message) {
        SheinCustomAttributePermission result = new SheinCustomAttributePermission();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinAddCustomAttributeValue createFailedAddCustomAttributeValue(String message) {
        SheinAddCustomAttributeValue result = new SheinAddCustomAttributeValue();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinUploadPic createFailedUploadPic(String message) {
        SheinUploadPic result = new SheinUploadPic();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinTransformPic createFailedTransformPic(String message) {
        SheinTransformPic result = new SheinTransformPic();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

    private SheinSiteList createFailedSiteList(String message) {
        SheinSiteList result = new SheinSiteList();
        result.setCode("-1");
        result.setMsg(message);
        return result;
    }

}
