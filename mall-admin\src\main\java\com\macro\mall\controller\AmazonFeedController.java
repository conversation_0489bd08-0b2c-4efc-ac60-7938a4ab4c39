package com.macro.mall.controller;

import com.macro.mall.dto.AmazonFeedRequest;
import com.macro.mall.service.AmazonFeedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/amazon/feed")
public class AmazonFeedController {
    @Autowired
    private AmazonFeedService amazonFeedService;

    @PostMapping("/upload")
    public ResponseEntity<?> uploadFeed(@RequestBody AmazonFeedRequest request) {
        try {
            String feedId = amazonFeedService.uploadListing(request);
            return ResponseEntity.ok("上架成功，Feed ID: " + feedId);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("上架失败: " + e.getMessage());
        }
    }
}
