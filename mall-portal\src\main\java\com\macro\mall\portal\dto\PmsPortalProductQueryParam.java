package com.macro.mall.portal.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
@Data
@EqualsAndHashCode
public class PmsPortalProductQueryParam {
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("密码")
    private String password;
//    @ApiModelProperty("上架状态")
//    private Integer publishStatus;
//    @ApiModelProperty("审核状态")
//    private Integer verifyStatus;
    @ApiModelProperty("商品名称模糊关键字")
    private String keyword;
//    @ApiModelProperty("商品扫描码")
//    private String productSn;
//    @ApiModelProperty("商品货号")
//    private String productCode;
    @ApiModelProperty("商品分类编号")
    private Long productCategoryId;
    @ApiModelProperty("商品父级分类编号")
    private Long productCateUplevelId;
    @ApiModelProperty("商品品牌编号")
    private Long brandId;
    @ApiModelProperty("仓库")
    private String warehouseId;
    @ApiModelProperty("国家")
    private String location;
    @ApiModelProperty("新品状态:0->不是新品；1->新品")
    private Integer NewStatus;
    @ApiModelProperty("推荐状态；0->不推荐；1->推荐")
    private Integer RecommandStatus;
    @ApiModelProperty("开始月份")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date StartMonth;  // 新增字段：上架时间
    @ApiModelProperty("结束月份")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date EndMonth;  // 新增字段：上架时间
    @ApiModelProperty(value = "最小体积（立方厘米）")
    private BigDecimal minSize;
    @ApiModelProperty(value = "最大体积（立方厘米）")
    private BigDecimal maxSize;
    @ApiModelProperty(value = "最小重量（克）")
    private BigDecimal minWeight;
    @ApiModelProperty(value = "最大重量（克）")
    private BigDecimal maxWeight;
    @ApiModelProperty(value = "最低价格")
    private BigDecimal minPrice;
    @ApiModelProperty(value = "最高价格")
    private BigDecimal maxPrice;
}