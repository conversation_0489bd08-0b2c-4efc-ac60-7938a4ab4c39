package com.macro.mall.service.impl;

import com.macro.mall.dto.PendingPublishDTO;
import com.macro.mall.mapper.PendingPublishMapper;
import com.macro.mall.mapper.PmsProductMapper;
import com.macro.mall.mapper.SheinAuthMapper;
import com.macro.mall.model.PendingPublishEntity;
import com.macro.mall.model.PlatformEnum;
import com.macro.mall.model.PmsProduct;
import com.macro.mall.model.UmsAdmin;
import com.macro.mall.service.PendingPublishService;
import com.macro.mall.service.UmsAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PendingPublishServiceImpl implements PendingPublishService {
    @Autowired
    private PendingPublishMapper pendingPublishMapper;
    @Autowired
    private PmsProductMapper pmsProductMapper;
    @Autowired
    private SheinAuthMapper sheinAuthMapper;
    @Autowired
    private UmsAdminService umsAdminService;

    @Override
    public List<Map<Long, String>> getPlatformList() {
        return PlatformEnum.getAllPlatforms();
    }

    @Override
    public List<Integer> getShopsByPlatform(Long platformId) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(username);

        if (admin == null) {
            log.error("用户不存在，用户名: {}", username);
            return Collections.emptyList(); // 更安全
        }

        Long adminId = admin.getId();
        List<Integer> supplierIds;

        switch (platformId.intValue()) {
            case 1:
                // 平台ID 1: Shein
                supplierIds = sheinAuthMapper.selectSupplierIdByUserId(adminId);
                break;
            case 2:
                // TODO: 其他平台的 mapper 查询
                //supplierIds = otherPlatformMapper.selectSupplierIdByUserId(adminId);
                supplierIds = Collections.emptyList();
                break;
            default:
                log.warn("不支持的平台ID: {}", platformId);
                supplierIds = Collections.emptyList();
        }
        return supplierIds;
    }

    @Override
    @Transactional
    public void addPendingPublish(PendingPublishDTO pendingPublishDTO) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(username);

        if (admin == null) {
            log.error("用户不存在，用户名: {}", username);
        }

        Long adminId = admin.getId();
        PendingPublishEntity pendingPublish = new PendingPublishEntity();
        BeanUtils.copyProperties(pendingPublishDTO, pendingPublish);
        pendingPublish.setUserId(adminId);
        log.info("添加购物车，用户ID: {}, 商品ID: {}, 商品名称: {}, 平台ID: {}, 店铺ID: {}, 上架数量: {}",
                adminId, pendingPublish.getProductId(), pendingPublish.getName(),
                pendingPublish.getPlatformId(), pendingPublish.getSupplierId(), pendingPublish.getNumber());
        PmsProduct pmsProduct = pmsProductMapper.selectByProductId(pendingPublish.getProductId());
        if (pmsProduct == null) {
            log.error("商品不存在，商品ID: {}", pendingPublish.getProductId());
            throw new RuntimeException("商品不存在");
        }
        //判断库存是否充足
        if (pmsProduct.getStock() < pendingPublish.getNumber()) {
            log.error("商品库存不足，商品ID: {}, 商品名称: {}, 商品编码: {}, 库存: {}, 需要: {}",
                    pendingPublish.getProductId(), pmsProduct.getName(), pmsProduct.getProductCode(),
                    pmsProduct.getStock(), pendingPublish.getNumber());
            throw new RuntimeException(String.format("商品库存不足，至多 %d 件", pmsProduct.getStock()));
        }
        // 更新商品库存
        PmsProduct product = new PmsProduct();
        product.setId(pmsProduct.getId());
        product.setStock(pmsProduct.getStock() - pendingPublish.getNumber());
        pmsProductMapper.updateById(product);
        List<PendingPublishEntity> pendingPublishList = pendingPublishMapper.selectByUserIdAndProductId(pendingPublish);
        if (pendingPublishList != null && pendingPublishList.size() > 0) {
            PendingPublishEntity pendingPublishEntity = pendingPublishList.get(0);
            pendingPublishEntity.setNumber(pendingPublish.getNumber());
            pendingPublishMapper.updateNumberById(pendingPublishEntity);
            return;
        }
        pendingPublish.setName(pmsProduct.getName());
        pendingPublish.setStatus(0);
        pendingPublish.setCreatedAt(LocalDateTime.now());
        pendingPublish.setUpdatedAt(LocalDateTime.now());
        pendingPublishMapper.insert(pendingPublish);
        return;
    }

    @Override
    public void cleanPendingPublish() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(username);

        if (admin == null) {
            log.error("用户不存在，用户名: {}", username);
        }

        Long adminId = admin.getId();
        pendingPublishMapper.deleteByUserId(adminId);
    }

    @Override
    public List<PendingPublishEntity> showPendingPublishList() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(username);

        if (admin == null) {
            log.error("用户不存在，用户名: {}", username);
        }

        Long adminId = admin.getId();
        PendingPublishEntity pendingPublish = new PendingPublishEntity();
        pendingPublish.setUserId(adminId);
        return pendingPublishMapper.selectByUserIdAndProductId(pendingPublish);
    }

    @Override
    public boolean cleanPendingPublishByProductId(Long productId) {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        UmsAdmin admin = umsAdminService.getAdminByUsername(username);

        if (admin == null) {
            log.error("用户不存在，用户名: {}", username);
            throw new RuntimeException("用户未登录或不存在");
        }

        Long userId = admin.getId();

        // 删除当前用户该商品的记录
        int deleted = pendingPublishMapper.deleteByUserIdAndProductId(userId, productId);
        return deleted > 0;
    }

}
