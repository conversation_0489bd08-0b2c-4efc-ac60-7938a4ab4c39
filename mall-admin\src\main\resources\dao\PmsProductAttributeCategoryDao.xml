<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.dao.PmsProductAttributeCategoryDao">
    <resultMap id="getListWithAttrMap" type="com.macro.mall.dto.PmsProductAttributeCategoryItem" extends="com.macro.mall.mapper.PmsProductAttributeCategoryMapper.BaseResultMap">
        <collection property="productAttributeList" columnPrefix="attr_" resultMap="com.macro.mall.mapper.PmsProductAttributeMapper.BaseResultMap">
        </collection>
    </resultMap>
    <select id="getListWithAttr" resultMap="getListWithAttrMap">
        SELECT
            pac.id,
            pac.name,
            pa.id attr_id,
            pa.name attr_name
        FROM
            pms_product_attribute_category pac
            LEFT JOIN pms_product_attribute pa ON pac.id = pa.product_attribute_category_id
        AND pa.type=1;
    </select>
</mapper>