package com.macro.mall.service;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.Sheindto.SheinProductConfig;

import java.util.List;

/**
 * SHEIN商品配置Service接口
 */
public interface SheinProductConfigService {

    /**
     * 创建商品配置
     * @param config 配置信息
     * @return 操作结果
     */
    CommonResult<SheinProductConfig> createConfig(SheinProductConfig config);

    /**
     * 根据商品ID查询配置
     * @param productId 商品ID
     * @return 配置信息
     */
    CommonResult<SheinProductConfig> getConfigByProductId(Long productId);

    /**
     * 根据商品ID更新配置
     * @param productId 商品ID
     * @param config 配置信息
     * @return 操作结果
     */
    CommonResult<SheinProductConfig> updateConfigByProductId(Long productId, SheinProductConfig config);

    /**
     * 根据商品ID删除配置
     * @param productId 商品ID
     * @return 操作结果
     */
    CommonResult<String> deleteConfigByProductId(Long productId);

    /**
     * 分页查询配置列表
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 配置列表
     */
    CommonResult<List<SheinProductConfig>> listConfigs(Integer pageNum, Integer pageSize);

    /**
     * 查询所有配置列表
     * @return 配置列表
     */
    CommonResult<List<SheinProductConfig>> getAllConfigs();
}
