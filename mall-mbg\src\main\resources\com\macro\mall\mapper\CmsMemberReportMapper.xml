<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsMemberReportMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsMemberReport">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="report_type" jdbcType="INTEGER" property="reportType" />
    <result column="report_member_name" jdbcType="VARCHAR" property="reportMemberName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="report_object" jdbcType="VARCHAR" property="reportObject" />
    <result column="report_status" jdbcType="INTEGER" property="reportStatus" />
    <result column="handle_status" jdbcType="INTEGER" property="handleStatus" />
    <result column="note" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_type, report_member_name, create_time, report_object, report_status, handle_status, 
    note
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsMemberReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_member_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsMemberReportExample">
    delete from cms_member_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsMemberReport">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_member_report (report_type, report_member_name, create_time, 
      report_object, report_status, handle_status, 
      note)
    values (#{reportType,jdbcType=INTEGER}, #{reportMemberName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{reportObject,jdbcType=VARCHAR}, #{reportStatus,jdbcType=INTEGER}, #{handleStatus,jdbcType=INTEGER}, 
      #{note,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsMemberReport">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_member_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportType != null">
        report_type,
      </if>
      <if test="reportMemberName != null">
        report_member_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="reportObject != null">
        report_object,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
      <if test="handleStatus != null">
        handle_status,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportType != null">
        #{reportType,jdbcType=INTEGER},
      </if>
      <if test="reportMemberName != null">
        #{reportMemberName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportObject != null">
        #{reportObject,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsMemberReportExample" resultType="java.lang.Long">
    select count(*) from cms_member_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_member_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportType != null">
        report_type = #{record.reportType,jdbcType=INTEGER},
      </if>
      <if test="record.reportMemberName != null">
        report_member_name = #{record.reportMemberName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportObject != null">
        report_object = #{record.reportObject,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null">
        report_status = #{record.reportStatus,jdbcType=INTEGER},
      </if>
      <if test="record.handleStatus != null">
        handle_status = #{record.handleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_member_report
    set id = #{record.id,jdbcType=BIGINT},
      report_type = #{record.reportType,jdbcType=INTEGER},
      report_member_name = #{record.reportMemberName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      report_object = #{record.reportObject,jdbcType=VARCHAR},
      report_status = #{record.reportStatus,jdbcType=INTEGER},
      handle_status = #{record.handleStatus,jdbcType=INTEGER},
      note = #{record.note,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>