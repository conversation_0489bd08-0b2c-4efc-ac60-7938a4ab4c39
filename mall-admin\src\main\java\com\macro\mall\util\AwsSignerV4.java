package com.macro.mall.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

public class AwsSignerV4 {

    private static final String HMAC_SHA256 = "HmacSHA256";

    private String accessKey;
    private String secretKey;
    private String region;
    private String service;

    public AwsSignerV4(String accessKey, String secretKey, String region, String service) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.region = region;
        this.service = service;
    }

    public Map<String, String> sign(String method, String canonicalUri, String queryString,
                                    Map<String, String> headers, String payload) throws Exception {
        // 时间
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        dateTimeFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        String amzDate = dateTimeFormat.format(new Date());

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        String dateStamp = dateFormat.format(new Date());

        // 添加必要header
        headers.put("x-amz-date", amzDate);
        if (!headers.containsKey("host")) {
            throw new RuntimeException("host header must be set");
        }

        String payloadHash = sha256Hex(payload);
        headers.put("x-amz-content-sha256", payloadHash);

        // 1. Canonical Request
        String canonicalHeaders = getCanonicalHeaders(headers);
        String signedHeaders = getSignedHeaders(headers);

        String canonicalRequest = method + "\n" +
                canonicalUri + "\n" +
                queryString + "\n" +
                canonicalHeaders + "\n" +
                signedHeaders + "\n" +
                payloadHash;

        String algorithm = "AWS4-HMAC-SHA256";
        String credentialScope = dateStamp + "/" + region + "/" + service + "/" + "aws4_request";

        String stringToSign = algorithm + "\n" +
                amzDate + "\n" +
                credentialScope + "\n" +
                sha256Hex(canonicalRequest);

        // 2. 签名计算
        byte[] signingKey = getSignatureKey(secretKey, dateStamp, region, service);
        String signature = toHex(hmacSha256(stringToSign, signingKey));

        // 3. 构造 Authorization header
        String authorizationHeader = algorithm + " " +
                "Credential=" + accessKey + "/" + credentialScope + ", " +
                "SignedHeaders=" + signedHeaders + ", " +
                "Signature=" + signature;

        Map<String, String> resultHeaders = new HashMap<>(headers);
        resultHeaders.put("Authorization", authorizationHeader);

        return resultHeaders;
    }

    private static String getCanonicalHeaders(Map<String, String> headers) {
        List<String> keys = new ArrayList<>(headers.keySet());
        Collections.sort(keys, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key.toLowerCase()).append(":").append(headers.get(key).trim()).append("\n");
        }
        return sb.toString();
    }

    private static String getSignedHeaders(Map<String, String> headers) {
        List<String> keys = new ArrayList<>(headers.keySet());
        Collections.sort(keys, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            if (i > 0) sb.append(";");
            sb.append(keys.get(i).toLowerCase());
        }
        return sb.toString();
    }

    private static byte[] hmacSha256(String data, byte[] key) throws Exception {
        Mac mac = Mac.getInstance(HMAC_SHA256);
        mac.init(new SecretKeySpec(key, HMAC_SHA256));
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    private static String toHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    private static byte[] getSignatureKey(String key, String dateStamp, String regionName, String serviceName) throws Exception {
        byte[] kDate = hmacSha256(dateStamp, ("AWS4" + key).getBytes(StandardCharsets.UTF_8));
        byte[] kRegion = hmacSha256(regionName, kDate);
        byte[] kService = hmacSha256(serviceName, kRegion);
        byte[] kSigning = hmacSha256("aws4_request", kService);
        return kSigning;
    }

    private static String sha256Hex(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] digest = md.digest(data.getBytes(StandardCharsets.UTF_8));
        return toHex(digest);
    }
}
