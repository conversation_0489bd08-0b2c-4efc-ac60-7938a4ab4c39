<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CmsTopicCommentMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.CmsTopicComment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="member_nick_name" jdbcType="VARCHAR" property="memberNickName" />
    <result column="topic_id" jdbcType="BIGINT" property="topicId" />
    <result column="member_icon" jdbcType="VARCHAR" property="memberIcon" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, member_nick_name, topic_id, member_icon, content, create_time, show_status
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.CmsTopicCommentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cms_topic_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cms_topic_comment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cms_topic_comment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.CmsTopicCommentExample">
    delete from cms_topic_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.CmsTopicComment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic_comment (member_nick_name, topic_id, member_icon, 
      content, create_time, show_status
      )
    values (#{memberNickName,jdbcType=VARCHAR}, #{topicId,jdbcType=BIGINT}, #{memberIcon,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{showStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.CmsTopicComment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cms_topic_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="memberNickName != null">
        member_nick_name,
      </if>
      <if test="topicId != null">
        topic_id,
      </if>
      <if test="memberIcon != null">
        member_icon,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="memberNickName != null">
        #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="topicId != null">
        #{topicId,jdbcType=BIGINT},
      </if>
      <if test="memberIcon != null">
        #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.CmsTopicCommentExample" resultType="java.lang.Long">
    select count(*) from cms_topic_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cms_topic_comment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.memberNickName != null">
        member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.topicId != null">
        topic_id = #{record.topicId,jdbcType=BIGINT},
      </if>
      <if test="record.memberIcon != null">
        member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.showStatus != null">
        show_status = #{record.showStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cms_topic_comment
    set id = #{record.id,jdbcType=BIGINT},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      topic_id = #{record.topicId,jdbcType=BIGINT},
      member_icon = #{record.memberIcon,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      show_status = #{record.showStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.CmsTopicComment">
    update cms_topic_comment
    <set>
      <if test="memberNickName != null">
        member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="topicId != null">
        topic_id = #{topicId,jdbcType=BIGINT},
      </if>
      <if test="memberIcon != null">
        member_icon = #{memberIcon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.CmsTopicComment">
    update cms_topic_comment
    set member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      topic_id = #{topicId,jdbcType=BIGINT},
      member_icon = #{memberIcon,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      show_status = #{showStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>