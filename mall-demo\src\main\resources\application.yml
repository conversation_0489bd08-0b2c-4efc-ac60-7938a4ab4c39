server:
  port: 8082

spring:
  application:
    name: mall-demo
  datasource:
    url: *************************************************************************************************************************
    username: root
    password: Test1234!
  thymeleaf:
    mode: HTML5
    encoding: utf-8
    servlet:
      content-type: text/html
    cache: false #开发时关闭缓存,不然没法看到实时页面
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

mybatis:
  mapper-locations:
    - classpath:mapper/*.xml
    - classpath*:com/**/mapper/*.xml

logging:
  level:
    root: info
    com.macro.mall: debug

host:
  mall:
   admin: http://localhost:8080

k5:
  Verify:
    Clientid: BetaLogisz
    Token: RFRESA==eV9dlEHB9uG8bIwv2p0g
    ClientidUPS: BetaUPS
    TokenUPS: RFRESA==jfeNnSl4UM98dcqgYI03
  url: http://dtdh.kingtrans.net/PostInterfaceService?method=
