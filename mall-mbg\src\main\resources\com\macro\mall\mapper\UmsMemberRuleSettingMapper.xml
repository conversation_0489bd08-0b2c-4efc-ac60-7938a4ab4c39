<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.UmsMemberRuleSettingMapper">
  <resultMap id="BaseResultMap" type="com.macro.mall.model.UmsMemberRuleSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="continue_sign_day" jdbcType="INTEGER" property="continueSignDay" />
    <result column="continue_sign_point" jdbcType="INTEGER" property="continueSignPoint" />
    <result column="consume_per_point" jdbcType="DECIMAL" property="consumePerPoint" />
    <result column="low_order_amount" jdbcType="DECIMAL" property="lowOrderAmount" />
    <result column="max_point_per_order" jdbcType="INTEGER" property="maxPointPerOrder" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, continue_sign_day, continue_sign_point, consume_per_point, low_order_amount, 
    max_point_per_order, type
  </sql>
  <select id="selectByExample" parameterType="com.macro.mall.model.UmsMemberRuleSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ums_member_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ums_member_rule_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ums_member_rule_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.macro.mall.model.UmsMemberRuleSettingExample">
    delete from ums_member_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.macro.mall.model.UmsMemberRuleSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_rule_setting (continue_sign_day, continue_sign_point, 
      consume_per_point, low_order_amount, max_point_per_order, 
      type)
    values (#{continueSignDay,jdbcType=INTEGER}, #{continueSignPoint,jdbcType=INTEGER}, 
      #{consumePerPoint,jdbcType=DECIMAL}, #{lowOrderAmount,jdbcType=DECIMAL}, #{maxPointPerOrder,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.macro.mall.model.UmsMemberRuleSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ums_member_rule_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="continueSignDay != null">
        continue_sign_day,
      </if>
      <if test="continueSignPoint != null">
        continue_sign_point,
      </if>
      <if test="consumePerPoint != null">
        consume_per_point,
      </if>
      <if test="lowOrderAmount != null">
        low_order_amount,
      </if>
      <if test="maxPointPerOrder != null">
        max_point_per_order,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="continueSignDay != null">
        #{continueSignDay,jdbcType=INTEGER},
      </if>
      <if test="continueSignPoint != null">
        #{continueSignPoint,jdbcType=INTEGER},
      </if>
      <if test="consumePerPoint != null">
        #{consumePerPoint,jdbcType=DECIMAL},
      </if>
      <if test="lowOrderAmount != null">
        #{lowOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="maxPointPerOrder != null">
        #{maxPointPerOrder,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.macro.mall.model.UmsMemberRuleSettingExample" resultType="java.lang.Long">
    select count(*) from ums_member_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ums_member_rule_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.continueSignDay != null">
        continue_sign_day = #{record.continueSignDay,jdbcType=INTEGER},
      </if>
      <if test="record.continueSignPoint != null">
        continue_sign_point = #{record.continueSignPoint,jdbcType=INTEGER},
      </if>
      <if test="record.consumePerPoint != null">
        consume_per_point = #{record.consumePerPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.lowOrderAmount != null">
        low_order_amount = #{record.lowOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.maxPointPerOrder != null">
        max_point_per_order = #{record.maxPointPerOrder,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ums_member_rule_setting
    set id = #{record.id,jdbcType=BIGINT},
      continue_sign_day = #{record.continueSignDay,jdbcType=INTEGER},
      continue_sign_point = #{record.continueSignPoint,jdbcType=INTEGER},
      consume_per_point = #{record.consumePerPoint,jdbcType=DECIMAL},
      low_order_amount = #{record.lowOrderAmount,jdbcType=DECIMAL},
      max_point_per_order = #{record.maxPointPerOrder,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.model.UmsMemberRuleSetting">
    update ums_member_rule_setting
    <set>
      <if test="continueSignDay != null">
        continue_sign_day = #{continueSignDay,jdbcType=INTEGER},
      </if>
      <if test="continueSignPoint != null">
        continue_sign_point = #{continueSignPoint,jdbcType=INTEGER},
      </if>
      <if test="consumePerPoint != null">
        consume_per_point = #{consumePerPoint,jdbcType=DECIMAL},
      </if>
      <if test="lowOrderAmount != null">
        low_order_amount = #{lowOrderAmount,jdbcType=DECIMAL},
      </if>
      <if test="maxPointPerOrder != null">
        max_point_per_order = #{maxPointPerOrder,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.macro.mall.model.UmsMemberRuleSetting">
    update ums_member_rule_setting
    set continue_sign_day = #{continueSignDay,jdbcType=INTEGER},
      continue_sign_point = #{continueSignPoint,jdbcType=INTEGER},
      consume_per_point = #{consumePerPoint,jdbcType=DECIMAL},
      low_order_amount = #{lowOrderAmount,jdbcType=DECIMAL},
      max_point_per_order = #{maxPointPerOrder,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>