package com.macro.mall.portal.controller;

import com.alibaba.fastjson.JSON;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.mapper.ApiCallLogMapper;
import com.macro.mall.model.ApiCallLog;
import com.macro.mall.model.OmsOrder;
import com.macro.mall.model.OmsOrderCreateParam;
import com.macro.mall.portal.component.ApiLogLimiter;
import com.macro.mall.portal.domain.*;
import com.macro.mall.portal.service.AdminLogService;
import com.macro.mall.portal.service.OmsPortalOrderService;
import com.macro.mall.portal.service.UmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单管理Controller
 * Created by macro on 2018/8/30.
 */
@Controller
@Api(tags = "OmsPortalOrderController")
@Tag(name = "OmsPortalOrderController", description = "订单管理")
@RequestMapping("/order")
public class OmsPortalOrderController {
    @Autowired
    private OmsPortalOrderService portalOrderService;
    @Autowired
    private UmsMemberService memberService;
    @Autowired
    private ApiCallLogMapper apiCallLogMapper;
    @Autowired
    private ApiLogLimiter apiLogLimiter;
    @Autowired
    private AdminLogService adminLogService;

    @ApiOperation("创建订单oms_order")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<OmsOrder> createOrder(HttpServletRequest http,
                                              @RequestBody OrderCreateRequest request) {
        String username = request.getUsername();
        String apiPath = "/create";
        String httpMethod = "POST";
        String operation = "创建订单";
        String ip = http.getRemoteAddr();
        String logKey = username + "@" + ip;
        Date now = new Date();

        if (!apiLogLimiter.allow(logKey)) {
            return CommonResult.failed("请求过于频繁，请稍后再试");
        }

        // 生成订单摘要
        String requestData;
        if (request.getCreatorder() == null) {
            requestData = "creatorder=null";
        } else {
            requestData = "orderSn=" + request.getCreatorder().getOrderSn()
                    + ", createOrder=" + request.getCreatorder().getOrderChannelCode()
                    + ", note=" + request.getCreatorder().getNote();
        }

        try {
            boolean authenticated = memberService.verifyMemberCredentials(username, request.getPassword());
            if (!authenticated) {
                adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（认证失败）", requestData, ip, now);
                return CommonResult.failed("认证失败：用户名或密码错误");
            }

            OmsOrder order = portalOrderService.createOrder(request.getCreatorder());
            adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（创建成功）", requestData, ip, now);
            return CommonResult.success(order, "创建成功");

        } catch (IllegalArgumentException e) {
            adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（参数错误）", e.getMessage(), ip, now);
            return CommonResult.failed("参数错误：" + e.getMessage());

        } catch (RuntimeException e) {
            adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（业务错误）", e.getMessage(), ip, now);
            return CommonResult.failed("业务错误：" + e.getMessage());

        } catch (Exception e) {
            adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（系统异常）", e.getMessage(), ip, now);
            return CommonResult.failed("系统异常：" + e.getMessage());
        }
    }

    @ApiOperation("获取订单物流详情")
    @PostMapping("/detail")
    @ResponseBody
    public CommonResult<OmsOrderDeliveryDetail> detail(HttpServletRequest http,
                                                       @RequestBody OrderDetailRequest request) {
        String username = request.getUsername();
        String apiPath = "/detail";
        String httpMethod = "POST";
        String operation = "获取订单物流详情";
        String ip = http.getRemoteAddr();
        Date now = new Date();

        String requestData = "orderSn=" + request.getOrderSn() + ", orderChannelCode=" + request.getOrderChannelCode();
        String logKey = username + "@" + ip;

        // 直接限制接口访问次数
        if (!apiLogLimiter.allow(logKey)) {
            return CommonResult.failed("请求过于频繁，请稍后再试");
        }

        try {
            boolean authenticated = memberService.verifyMemberCredentials(username, request.getPassword());
            if (!authenticated) {
                adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（认证失败）", requestData, ip, now);
                return CommonResult.failed("认证失败：用户名或密码错误");
            }

            OmsOrderDeliveryDetail orderDetail = portalOrderService.detail(request.getOrderSn(), request.getOrderChannelCode());
            if (orderDetail != null) {
                adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（查询成功）", requestData, ip, now);
                return CommonResult.success(orderDetail, "查询成功");
            } else {
                adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（订单不存在）", requestData, ip, now);
                return CommonResult.failed("订单不存在");
            }
        } catch (Exception e) {
            adminLogService.saveApiLogAsync(username, apiPath, httpMethod, operation + "（异常）", requestData, ip, now);
            return CommonResult.failed("查询订单物流详情失败");
        }
    }


    @ApiOperation("修改收货人信息")
    @RequestMapping(value = "/update/receiverInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult updateReceiverInfo(@RequestBody ReceiverInfoUpdateRequest request) {
        try {
            boolean authenticated = memberService.verifyMemberCredentials(
                    request.getUsername(), request.getPassword()
            );
            if (!authenticated) {
                return CommonResult.failed("认证失败：用户名或密码错误");
            }

            int count = portalOrderService.updateReceiverInfo(request.getReceiverInfoParam());
            if (count > 0) {
                return CommonResult.success(count);
            }
            return CommonResult.failed("订单不存在或更新失败");
        } catch (Exception e) {
            return CommonResult.failed("修改收货人信息失败");
        }
    }

    @ApiOperation("用户确认收货")
    @RequestMapping(value = "/confirmReceiveOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult confirmReceiveOrder(@RequestBody OrderActionRequest request) {
        try {
            boolean authenticated = memberService.verifyMemberCredentials(
                    request.getUsername(), request.getPassword()
            );
            if (!authenticated) {
                return CommonResult.failed("认证失败：用户名或密码错误");
            }

            portalOrderService.confirmReceiveOrder(request.getOrderSn(), request.getOrderChannelCode());
            return CommonResult.success(null, "确认收货成功");
        } catch (Exception e) {
            return CommonResult.failed("确认收货失败");
        }
    }

    @ApiOperation("用户取消订单")
    @RequestMapping(value = "/cancelUserOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult cancelUserOrder(@RequestBody OrderActionRequest request) {
        try {
            boolean authenticated = memberService.verifyMemberCredentials(
                    request.getUsername(), request.getPassword()
            );
            if (!authenticated) {
                return CommonResult.failed("认证失败：用户名或密码错误");
            }

            portalOrderService.cancelOrder(request.getOrderSn(), request.getOrderChannelCode());
            return CommonResult.success(null, "取消订单成功");
        } catch (Exception e) {
            return CommonResult.failed("取消订单失败");
        }
    }

    @ApiOperation("用户支付成功的回调")
    @RequestMapping(value = "/paySuccess", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult paySuccess(@RequestParam Long orderId,@RequestParam Integer payType) {
        Integer count = portalOrderService.paySuccess(orderId,payType);
        return CommonResult.success(count, "支付成功");
    }

    @ApiOperation("自动取消超时订单")
    @RequestMapping(value = "/cancelTimeOutOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult cancelTimeOutOrder() {
        portalOrderService.cancelTimeOutOrder();
        return CommonResult.success(null);
    }

    @ApiOperation("取消单个超时订单")
    @RequestMapping(value = "/cancelOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult cancelOrder(Long orderId) {
        portalOrderService.sendDelayMessageCancelOrder(orderId);
        return CommonResult.success(null);
    }

    @ApiOperation("按状态分页获取用户订单列表")
    @ApiImplicitParam(name = "status", value = "订单状态：-1->全部；0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭",
            defaultValue = "-1", allowableValues = "-1,0,1,2,3,4", paramType = "query", dataType = "int")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<OmsOrderDetail>> list(@RequestParam Integer status,
                                                   @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                   @RequestParam(required = false, defaultValue = "5") Integer pageSize) {
        CommonPage<OmsOrderDetail> orderPage = portalOrderService.list(status,pageNum,pageSize);
        return CommonResult.success(orderPage);
    }


    @ApiOperation("用户删除订单")
    @RequestMapping(value = "/deleteOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult deleteOrder(Long orderId) {
        portalOrderService.deleteOrder(orderId);
        return CommonResult.success(null);
    }
}
