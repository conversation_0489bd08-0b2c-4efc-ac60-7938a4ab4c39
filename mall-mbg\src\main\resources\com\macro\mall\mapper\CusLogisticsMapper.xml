<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.mapper.CusLogisticsMapper">
    <resultMap id="CusBaseLogisticsMap" type="com.macro.mall.model.CusBaseLogistics">
        <result property="id" column="id"/>
        <result property="receiveDate" column="receive_date"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="waybillNumber" column="waybill_number"/>
        <result property="customerOrderNumber" column="customer_order_number"/>
        <result property="fwTrackingNumber" column="fw_tracking_number"/>
        <result property="containerNumber" column="container_number"/>
        <result property="status" column="status"/>
        <result property="logisticsChannel" column="logistics_channel"/>
        <result property="loadingPort" column="loading_port"/>
        <result property="loadingTime" column="loading_time"/>
        <result property="arrivalPort" column="arrival_port"/>
        <result property="arrivalDate" column="arrival_date"/>
        <result property="latestTrackNotes" column="latest_track_notes"/>
        <result property="trackUpdateTime" column="track_update_time"/>
        <result property="customsClearanceMaterials" column="customs_clearance_materials"/>
        <result property="customsClearanceResult" column="customs_clearance_result"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <resultMap id="CusLogisticsMap" type="com.macro.mall.model.CusLogistics">
        <result property="id" column="id"/>
        <result property="receiveDate" column="receive_date"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="waybillNumber" column="waybill_number"/>
        <result property="customerOrderNumber" column="customer_order_number"/>
        <result property="fwTrackingNumber" column="fw_tracking_number"/>
        <result property="containerNumber" column="container_number"/>
        <result property="status" column="status"/>
        <result property="logisticsChannel" column="logistics_channel"/>
        <result property="loadingPort" column="loading_port"/>
        <result property="loadingTime" column="loading_time"/>
        <result property="arrivalPort" column="arrival_port"/>
        <result property="arrivalDate" column="arrival_date"/>
        <result property="targetPostcode" column="target_postcode"/>
        <result property="targetCountry" column="target_country"/>
        <result property="ecommercePlatform" column="ecommerce_platform"/>
        <result property="fulfillmentWarehouse" column="fulfillment_warehouse"/>
        <result property="isRemote" column="is_remote"/>
        <result property="ctns" column="ctns"/>
        <result property="tilPcs" column="til_pcs"/>
        <result property="netWeight" column="net_weight"/>
        <result property="grossWeight" column="gross_weight"/>
        <result property="measurement" column="measurement"/>
        <result property="billWeight" column="bill_weight"/>
        <result property="confirmedBillWeight" column="confirmed_bill_weight"/>
        <result property="cdType" column="cd_type"/>
        <result property="ccType" column="cc_type"/>
        <result property="clType" column="cl_type"/>
        <result property="material" column="material"/>
        <result property="additionalNotes" column="additional_notes"/>
        <result property="description" column="description"/>
        <result property="ccAmount" column="cc_amount"/>
        <result property="problemItemType" column="problem_item_type"/>
        <result property="ladingNumber" column="lading_number"/>
        <result property="latestTrackNotes" column="latest_track_notes"/>
        <result property="trackUpdateTime" column="track_update_time"/>
        <result property="customsClearanceMaterials" column="customs_clearance_materials"/>
        <result property="acceptanceChannelNo" column="acceptance_channel_no"/>
        <result property="ccCompanyNo" column="cc_company_no"/>
        <result property="fwLogisticsNo" column="fw_logistics_no"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <resultMap id="CusLogisticsHistoryMap" type="com.macro.mall.model.CusLogisticsHistory">
        <result property="id" column="id"/>
        <result property="logisticsId" column="logistics_id"/>
        <result property="note" column="note"/>
        <result property="trackUpdateTime" column="track_update_time"/>
    </resultMap>

    <select id="selectAllLogistics" resultMap="CusBaseLogisticsMap">
        SELECT *
        FROM logistics_records
        WHERE
        <choose>
            <when test="field == 'cc_companyno'">
                cc_companyno = #{username}
            </when>
            <when test="field == 'fw_logistics_no'">
                fw_logistics_no = #{username}
            </when>
            <when test="field == 'acceptance_channel_no'">
                acceptance_channel_no = #{username}
            </when>
            <otherwise>
                1 = 0
            </otherwise>
        </choose>

        <if test="startTime != null">
            AND create_time >= FROM_UNIXTIME(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            AND create_time &lt;= FROM_UNIXTIME(#{endTime} / 1000)
        </if>
        <if test="waybillNumber != null and waybillNumber != ''">
            AND (
            waybill_number LIKE CONCAT('%', #{waybillNumber}, '%')
            OR customer_order_number LIKE CONCAT('%', #{waybillNumber}, '%')
            OR fw_tracking_number LIKE CONCAT('%', #{waybillNumber}, '%')
            OR container_number LIKE CONCAT('%', #{waybillNumber}, '%')
            )
        </if>
    </select>
    <select id="ROOTselectAllLogistics" resultMap="CusBaseLogisticsMap">
        SELECT *
        FROM logistics_records
        <where>
            <if test="startTime != null">
                create_time >= FROM_UNIXTIME(#{startTime} / 1000)
            </if>
            <if test="endTime != null">
                create_time &lt;= FROM_UNIXTIME(#{endTime} / 1000)
            </if>
            <if test="waybillNumber != null and waybillNumber != ''">
                AND (
                waybill_number LIKE CONCAT('%', #{waybillNumber}, '%')
                OR customer_order_number LIKE CONCAT('%', #{waybillNumber}, '%')
                OR fw_tracking_number LIKE CONCAT('%', #{waybillNumber}, '%')
                OR container_number LIKE CONCAT('%', #{waybillNumber}, '%')
                )
            </if>
        </where>
    </select>
    <select id="querySingleLogistics" resultMap="CusLogisticsMap">
        SELECT *
        FROM logistics_records
        WHERE id = #{id}
        <choose>
            <when test="field == 'cc_companyno'">
                AND cc_companyno = #{username}
            </when>
            <when test="field == 'fw_logistics_no'">
                AND fw_logistics_no = #{username}
            </when>
            <when test="field == 'acceptance_channel_no'">
                AND acceptance_channel_no = #{username}
            </when>
            <otherwise>
                1 = 0
            </otherwise>
        </choose>

    </select>

    <select id="ROOTquerySingleLogistics" resultMap="CusLogisticsMap">
        SELECT *
        FROM logistics_records
        WHERE id = #{id}
    </select>

    <delete id="deleteByIds">
        DELETE FROM logistics_records
        WHERE id IN
        <foreach item="id" collection="ids" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <update id="updateStatusByIds">
        UPDATE logistics_records
        SET status = #{status}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="updateNoteByKeys">
        UPDATE logistics_records
        SET latest_track_notes = #{note},
        track_update_time = #{trackUpdateTime}
        WHERE id = #{id}
    </update>

    <!-- 查询是否存在记录 -->
    <select id="selectByWaybillAndCustomerOrder" resultMap="CusLogisticsMap">
        SELECT * FROM logistics_records
        WHERE waybill_number = #{waybillNumber}
        AND customer_order_number = #{customerOrderNumber}
        AND fw_tracking_number = #{fwTrackingNumber}
        AND acceptance_channel_no = #{username}
        LIMIT 1
    </select>

    <!-- 更新记录 -->
    <update id="updateByWaybillAndCustomerOrder">
        UPDATE logistics_records
        SET
        receive_date = #{receiveDate},
        receive_time = #{receiveTime},
        container_number = #{containerNumber},
        status = #{status},
        logistics_channel = #{logisticsChannel},
        loading_port = #{loadingPort},
        loading_time = #{loadingTime},
        arrival_port = #{arrivalPort},
        arrival_date = #{arrivalDate},
        target_postcode = #{targetPostcode},
        target_country = #{targetCountry},
        ecommerce_platform = #{ecommercePlatform},
        fulfillment_warehouse = #{fulfillmentWarehouse},
        track_update_time = NOW()  <!-- 设置当前时间作为更新时间 -->
        WHERE waybill_number = #{waybillNumber}
        AND customer_order_number = #{customerOrderNumber}
        AND fw_tracking_number = #{fwTrackingNumber}
    </update>

    <!-- 插入记录 -->
    <insert id="insertLogistics" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO logistics_records (
        receive_date, receive_time, waybill_number, customer_order_number, fw_tracking_number,
        container_number, status, logistics_channel, loading_port, loading_time,
        arrival_port, arrival_date, target_postcode, target_country, ecommerce_platform,
        fulfillment_warehouse, is_remote, ctns, til_pcs, net_weight,
        gross_weight, measurement, bill_weight, confirmed_bill_weight, cd_type,
        cc_type, cl_type, material, additional_notes, description,
        cc_amount, problem_item_type, lading_number, latest_track_notes, track_update_time,
        customs_clearance_materials, acceptance_channel_no, cc_companyno, fw_logistics_no, create_time
        ) VALUES (
        #{receiveDate}, #{receiveTime}, #{waybillNumber}, #{customerOrderNumber}, #{fwTrackingNumber},
        #{containerNumber}, #{status}, #{logisticsChannel}, #{loadingPort}, #{loadingTime},
        #{arrivalPort}, #{arrivalDate}, #{targetPostcode}, #{targetCountry}, #{ecommercePlatform},
        #{fulfillmentWarehouse}, #{isRemote}, #{ctns}, #{tilPcs}, #{netWeight},
        #{grossWeight}, #{measurement}, #{billWeight}, #{confirmedBillWeight}, #{cdType},
        #{ccType}, #{clType}, #{material}, #{additionalNotes}, #{description},
        #{ccAmount}, #{problemItemType}, #{ladingNumber}, #{latestTrackNotes}, #{trackUpdateTime},
        #{customsClearanceMaterials}, #{acceptanceChannelNo}, #{ccCompanyNo}, #{fwLogisticsNo}, #{createTime}
        )
    </insert>

    <select id="getLogisticsId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM logistics_records
        WHERE id = #{id}
    </select>

    <insert id="insertLogisticsHistory">
        INSERT INTO logistics_history (logistics_id, note, track_update_time, username, role)
        VALUES (#{id}, #{note}, #{trackUpdateTime}, #{username}, #{role})
    </insert>

    <select id="getLogisticsHistoryByLogisticsId" resultMap="CusLogisticsHistoryMap">
        SELECT id, logistics_id, note, track_update_time, role
        FROM logistics_history
        WHERE logistics_id = #{logisticsId}
    </select>
</mapper>