package com.macro.mall.Sheindto;

import lombok.Data;

import java.util.List;

/**
 * SHEIN店铺可选属性响应DTO
 */
@Data
public class SheinAttributeTemplate {
    
    /**
     * 响应码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应信息
     */
    private Info info;
    
    /**
     * 业务标识
     */
    private String bbl;
    
    /**
     * 追踪ID
     */
    private String traceId;
    
    @Data
    public static class Info {
        /**
         * 属性数据列表
         */
        private List<AttributeData> data;
        
        /**
         * 元数据信息
         */
        private Meta meta;
    }
    
    @Data
    public static class AttributeData {
        /**
         * 属性信息列表
         */
        private List<AttributeInfo> attributeInfos;
        
        /**
         * 供应商ID
         */
        private Long supplierId;
        
        /**
         * 产品类型ID
         */
        private Long productTypeId;
    }
    
    @Data
    public static class AttributeInfo {
        /**
         * 属性ID
         */
        private Long attributeId;
        
        /**
         * 属性名称
         */
        private String attributeName;
        
        /**
         * 属性是否显示
         */
        private Integer attributeIsShow;
        
        /**
         * 属性类型
         */
        private Integer attributeType;
        
        /**
         * 属性标签
         */
        private Integer attributeLabel;
        
        /**
         * 属性模式
         */
        private Integer attributeMode;
        
        /**
         * 属性输入数量
         */
        private Integer attributeInputNum;
        
        /**
         * 属性状态
         */
        private Integer attributeStatus;
        
        /**
         * 属性备注列表
         */
        private List<Integer> attributeRemarkList;
        
        /**
         * 属性值信息列表
         */
        private List<AttributeValueInfo> attributeValueInfoList;
    }
    
    @Data
    public static class AttributeValueInfo {
        /**
         * 属性值
         */
        private String attributeValue;
        
        /**
         * 属性值ID
         */
        private Long attributeValueId;
        
        /**
         * 是否显示
         */
        private Integer isShow;
        
        /**
         * 是否自定义属性值
         */
        private Boolean isCustomAttributeValue;
    }
    
    @Data
    public static class Meta {
        /**
         * 总数量
         */
        private Integer count;
        
        /**
         * 自定义对象
         */
        private Object customObj;
    }
}
